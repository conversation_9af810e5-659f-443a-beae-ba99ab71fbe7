import 'dart:async';
import 'dart:convert';
import 'dart:ui';
import 'package:dlyz_flutter/common/dl_color.dart';
import 'package:dlyz_flutter/manager/game_data_manager.dart';
import 'package:dlyz_flutter/manager/network_manager.dart';
import 'package:dlyz_flutter/net/http_service.dart';
import 'package:dlyz_flutter/net/api/forum_service.dart';
import 'package:dlyz_flutter/net/config/http_base_config.dart';
import 'package:dlyz_flutter/pages/community/community_page.dart';
import 'package:dlyz_flutter/pages/games/GamesPage.dart';
import 'package:dlyz_flutter/pages/profile/ProfilePage.dart';
import 'package:dlyz_flutter/pages/splash/SplashPage.dart';
import 'package:dlyz_flutter/pages/privacy/privacy_check_page.dart';
import 'package:dlyz_flutter/pages/demo/proxy_config_page.dart';
import 'package:dlyz_flutter/providers/bottom_nav_provider.dart';
import 'package:dlyz_flutter/track/track.dart';
import 'package:dlyz_flutter/track/forum_track.dart';
import 'package:dlyz_flutter/track/init_track.dart';
import 'package:dlyz_flutter/track/exit_track.dart';
import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:flutter/foundation.dart';
import 'package:dlyz_flutter/services/download/ALDownloader.dart';
import 'package:dlyz_flutter/utils/sp_utils.dart';
import 'package:dlyz_flutter/config/app_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
import 'bugless/app_error_handler.dart';
import 'providers/login_provider.dart';
import 'providers/user_provider.dart';
import 'providers/download_provider.dart';
import 'providers/app_review_provider.dart';
import 'providers/game_circle_provider.dart';
import 'providers/game_circle_config_provider.dart';
import 'providers/function_area_provider.dart';
import 'providers/role_provider.dart';
import 'pages/games/GameCirclePage.dart';
import 'utils/event_bus.dart';
import 'events/event_bus_manager.dart';
import 'events/login_invalid_event.dart';
import 'components/common_alert.dart';
import 'pages/settings/AccountManagementPage.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  AppErrorHandler.run(
    const MyApp(),
    initFunc: () async {
      await initApp();
    },
  );
}

///初始化
Future<void> initApp() async {
  await SpManager.init();

  // 初始化应用配置
  await AppConfig.initialize();

  ALDownloader.initialize();
  ALDownloader.configurePrint(true, frequentEnabled: false);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // 在应用初始化时就设置NavigatorKey
  HttpService.getInstance().setNavigatorKey(navigatorKey);
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  StreamSubscription<LoginExpiredEvent>? _loginExpiredSubscription;
  StreamSubscription<LoginInvalidEvent>? _forumLoginInvalidSubscription;
  bool _isForumLoginInvalidDialogShowing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _setupGlobalLoginExpiryListener();
    _setupForumLoginInvalidListener();
  }

  @override
  void dispose() {
    // 销毁网络监听
    NetworkManager().dispose();
    // 取消登录失效事件监听
    _loginExpiredSubscription?.cancel();
    _forumLoginInvalidSubscription?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// 设置全局登录失效事件监听器
  void _setupGlobalLoginExpiryListener() {
    _loginExpiredSubscription = eventBus.on<LoginExpiredEvent>().listen((event) {
     _handleLoginExpired(event.message);
    });
  }

  /// 设置论坛登录失效事件监听器
  void _setupForumLoginInvalidListener() {
    _forumLoginInvalidSubscription = EventBusManager.instance.on<LoginInvalidEvent>((event) {
      _handleForumLoginInvalid(event);
    });
  }

  /// 处理全局登录失效事件
  void _handleLoginExpired(String message) {
    final currentContext = navigatorKey.currentContext;
    if (currentContext != null && mounted) {
      CommonAlert.show(
        context: currentContext,
        title: '提示',
        content: message,
        confirmText: '我知道了',
        singleButton: true,
        barrierDismissible: false, // 不允许点击外部关闭
        onConfirm: () {
          // 确定时清除用户数据并跳转到登录页面
          _performLogout();
        },
      );
    }
  }

  /// 处理论坛登录失效事件
  void _handleForumLoginInvalid(LoginInvalidEvent event) {
    // 防止重复弹窗
    if (_isForumLoginInvalidDialogShowing) {
      LogUtil.d('论坛登录失效弹窗已在显示中，忽略重复事件');
      return;
    }
    
    final currentContext = navigatorKey.currentContext;
    if (currentContext != null && mounted) {
      _isForumLoginInvalidDialogShowing = true;
      
      // 清除论坛登录状态
      ForumService.clearForumLoginState();
      
      CommonAlert.show(
        context: currentContext,
        title: '登录失效',
        content: '论坛登录已失效，请重新登录',
        confirmText: '去登录',
        singleButton: true,
        barrierDismissible: false,
        onConfirm: () {
          // 使用Future.delayed确保弹窗完全关闭后再执行跳转
          Future.delayed(Duration(milliseconds: 100), () {
            _isForumLoginInvalidDialogShowing = false;
            final context = navigatorKey.currentContext;
            if (context != null) {
              // 跳转到登录页面
              final route = MaterialPageRoute(
                builder: (context) => const AccountManagementPage(welcomeMode: true),
              );
              Navigator.of(context).push(route);
            }
          });
        },
      );
    }
  }

  void _performLogout() async {
    final currentContext = navigatorKey.currentContext;
    if (currentContext != null) {
      final userProvider = Provider.of<UserProvider>(currentContext, listen: false);
      await userProvider.logout(logoutType: LogoutType.manualExit);
      if (mounted && navigatorKey.currentContext != null) {
        final route = MaterialPageRoute(
          builder: (context) => const AccountManagementPage(welcomeMode: true),
        );
        Navigator.of(navigatorKey.currentContext!).push(route);
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用暂停或退出时暂停所有下载任务
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached) {
      ALDownloader.pauseAll();
      // 停止在线时长追踪
      ForumTrack.stopOnlineDurationTracking();
      // 刷新待上报埋点事件，确保在应用退出前完成上报
      InitTrack.flushPendingEvents();
      // 切后台埋点
      ExitTrack.trackBackstageSucc();
    } else if (state == AppLifecycleState.resumed) {
      // 应用恢复时重新启动在线时长追踪
      ForumTrack.startOnlineDurationTracking();
      // 切后台后回到游戏埋点
      ExitTrack.trackResumeSucc();
      ExitTrack.trackReturnApp();
    }
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LoginStateProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => DownloadProvider()),
        ChangeNotifierProvider(create: (_) => GameDataManager()),
        ChangeNotifierProvider(create: (_) => AppReviewProvider()),
        ChangeNotifierProvider(create: (_) => GameCircleProvider()),
        ChangeNotifierProvider(create: (_) => GameCircleConfigProvider()),
        ChangeNotifierProvider(create: (_) => FunctionAreaProvider()),
        ChangeNotifierProvider(create: (_) => RoleProvider()),
        ChangeNotifierProvider(create: (_) => BottomNavProvider()),
      ],
      child: MaterialApp(
        navigatorKey: navigatorKey,
        theme: ThemeData(
          // 全局背景色
          scaffoldBackgroundColor: Colors.white,
        ),
        initialRoute: '/',
        routes: {
          '/': (context) => const InitialPageResolver(),
          '/privacy-check': (context) => const PrivacyCheckPage(),
          '/splash': (context) => const SplashPage(),
          '/game-circle': (context) => const GameCirclePage(),
          '/home': (context) => const MyHomePage(),
        },
        onGenerateRoute: (RouteSettings settings) {
          // 这里可以根据需要添加其他动态路由
          return null;
        },
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _currentIndex = 0;
  DateTime? _lastBackPressTime;

  final List<Widget> _pages = const [
    CommunityPage(),
    GamesPage(),
    ProfilePage(),
  ];

  @override
  void initState() {
    super.initState();
    _initializeLoginProvider();
    _initializeHttpService();
    
    // 启动在线时长追踪
    ForumTrack.startOnlineDurationTracking();
  }

  @override
  void dispose() {
    // 停止在线时长追踪
    ForumTrack.stopOnlineDurationTracking();
    super.dispose();
  }

  Future<void> _initializeHttpService() async {
    HttpService.getInstance().setNavigatorKey(navigatorKey);
  }

  Future<void> _initializeLoginProvider() async {
    try {
      final loginProvider = Provider.of<LoginStateProvider>(
        context,
        listen: false,
      );
      await loginProvider.initialize();
      
      // 初始化游戏圈提供者并更新tgid
      final gameCircleProvider = Provider.of<GameCircleProvider>(
        context,
        listen: false,
      );
      await gameCircleProvider.initialize();
      
    } catch (e) {
      print('LoginProvider 初始化失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: _onWillPop,
      child: Scaffold(
        // 移除AppBar
        body: SafeArea(
          child: IndexedStack(index: _currentIndex, children: _pages),
        ),
        // 底部导航栏
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
              context.read<BottomNavProvider>().updateIndex(index);
            });
          },
          items: [
            BottomNavigationBarItem(
              icon: SvgPicture.asset('assets/images/home_icon.svg', width: 18, height: 18),
              activeIcon: SvgPicture.asset('assets/images/home_action_icon.svg', width: 18, height: 18),
              label: '圈子',
            ),
            BottomNavigationBarItem(
              icon: GameDataManager().floatConfig.floatMsg ? _buildIconWithBadge('assets/images/games_icon.svg') : SvgPicture.asset('assets/images/games_icon.svg', width: 18, height: 18),
              activeIcon: GameDataManager().floatConfig.floatMsg ? _buildIconWithBadge('assets/images/games_action_icon.svg') : SvgPicture.asset('assets/images/games_action_icon.svg', width: 18, height: 18),
              label: '游戏',
            ),
            BottomNavigationBarItem(
              icon: SvgPicture.asset('assets/images/profile_icon.svg', width: 18, height: 18),
              activeIcon: SvgPicture.asset('assets/images/profile_action_icon.svg', width: 18, height: 18),
              label: '我的',
            ),
          ],
          // 设置选中颜色
          selectedItemColor: DLColor.primary,
          // 设置未选中颜色
          unselectedItemColor: DLColor.textThird,
          // 显示选中和未选中的标签
          showSelectedLabels: true,
          showUnselectedLabels: true,
          // 移除底部导航栏自带的背景色
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),

      ),
    );
  }

  void _onWillPop(bool didPop, dynamic result) async {
    if (!didPop) {
      final now = DateTime.now();

      if (_lastBackPressTime == null ||
          now.difference(_lastBackPressTime!).inMilliseconds > 2000) {
        // 第一次按返回键或超过2秒
        _lastBackPressTime = now;

        Fluttertoast.showToast(
          msg: "再按一次退出应用",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
        );
      } else {
        // 第二次按返回键，退出应用
        SystemNavigator.pop();
      }
    }
  }

  Widget _buildIconWithBadge(String iconPath) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        SvgPicture.asset(iconPath, width: 18, height: 18),
        Positioned(
          right: -7,
          top: -4,
          child: Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ],
    );
  }
}

/// 初始页面解析器
/// 根据代理配置文件决定启动时显示哪个页面
class InitialPageResolver extends StatefulWidget {
  const InitialPageResolver({super.key});

  @override
  State<InitialPageResolver> createState() => _InitialPageResolverState();
}

class _InitialPageResolverState extends State<InitialPageResolver> {
  Widget? _targetPage;

  @override
  void initState() {
    super.initState();
    _resolveInitialPage();
  }

  /// 解析初始页面
  Future<void> _resolveInitialPage() async {
    Widget targetPage;

    if (kDebugMode) {
      // Debug模式下检查配置文件
      bool shouldShowProxyConfig = await _shouldShowProxyConfig();
      targetPage =
          shouldShowProxyConfig
              ? const ProxyConfigPage()
              : const PrivacyCheckPage();
    } else {
      // Release模式直接跳转隐私检查页面
      targetPage = const PrivacyCheckPage();
    }

    if (mounted) {
      setState(() {
        _targetPage = targetPage;
      });
    }
  }

  /// 检查是否应该显示代理配置页面
  Future<bool> _shouldShowProxyConfig() async {
    try {
      final String jsonString = await rootBundle.loadString(
        'assets/config/proxy_config.json',
      );
      final Map<String, dynamic> config = json.decode(jsonString);
      return config['isShow'] == true;
    } catch (e) {
      print('读取代理配置文件失败: $e');
      // 读取失败时默认不显示代理配置页面
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_targetPage == null) {
      // 配置加载中，显示加载界面
      return const Scaffold(
        backgroundColor: Colors.white,
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return _targetPage!;
  }
}
