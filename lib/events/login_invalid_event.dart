/// 登录失效事件
/// 当检测到论坛接口返回登录失效的错误码时触发
class LoginInvalidEvent {
  /// 错误码
  final int code;
  
  /// 错误信息
  final String message;
  
  /// 事件触发时间
  final DateTime timestamp;
  
  /// 触发的接口路径（可选）
  final String? apiPath;

  LoginInvalidEvent({
    required this.code,
    required this.message,
    this.apiPath,
  }) : timestamp = DateTime.now();

  @override
  String toString() {
    return 'LoginInvalidEvent{code: $code, message: $message, apiPath: $apiPath, timestamp: $timestamp}';
  }
}