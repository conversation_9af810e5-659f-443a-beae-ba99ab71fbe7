import 'dart:async';
import '../utils/log_util.dart';

/// 全局事件总线管理器
/// 用于在应用内进行解耦的事件通信
class EventBusManager {
  static final EventBusManager _instance = EventBusManager._internal();
  factory EventBusManager() => _instance;
  EventBusManager._internal();

  /// 获取单例实例
  static EventBusManager get instance => _instance;

  /// 事件流控制器
  final StreamController<dynamic> _eventController = StreamController<dynamic>.broadcast();

  /// 事件流
  Stream<dynamic> get eventStream => _eventController.stream;

  /// 发送事件
  void fire(dynamic event) {
    if (_eventController.isClosed) {
      LogUtil.w('EventBusManager: 尝试发送事件到已关闭的事件总线: $event');
      return;
    }
    
    LogUtil.d('EventBusManager: 发送事件 - ${event.runtimeType}: $event');
    _eventController.add(event);
  }

  /// 监听特定类型的事件
  StreamSubscription<T> on<T>(void Function(T event) onData) {
    return eventStream.where((event) => event is T).cast<T>().listen(onData);
  }

  /// 销毁事件总线
  void dispose() {
    LogUtil.d('EventBusManager: 销毁事件总线');
    _eventController.close();
  }
}

/// 用户数据变更事件
class UserDataChangedEvent {

  UserDataChangedEvent();

  @override
  String toString() {
    return 'UserDataChangedEvent';
  }
}