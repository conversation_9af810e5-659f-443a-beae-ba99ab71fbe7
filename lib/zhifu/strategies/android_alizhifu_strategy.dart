import 'dart:convert';

import 'package:dlyz_flutter/utils/log_util.dart';

import '../../manager/channel_manager.dart';
import '../zhifu_strategy_interface.dart';
import '../zhifu_type.dart';

class AndroidAliZhiFuStrategy implements IZhiFuStrategy {
  @override
  Future<Map<String, String>> zhiFu(Map<String, dynamic> params) async {
    String orderId = params['uuid'] ?? '';
    String tradeInfo = params['trade'] ?? '';
    try {
      List<int> bytes = base64Decode(tradeInfo);
      tradeInfo = utf8.decode(bytes);
      LogUtil.d("解码结果: $tradeInfo");
    } on FormatException catch (e) {
      LogUtil.e("Base64 格式错误: $e");
    } catch (e) {
      LogUtil.e("未知错误: $e");
    }
    if (tradeInfo.isEmpty) {
      return ZhiFuResult.buildReturnResult(ZhiFuResult.ZHIFU_FAILED, 'zhifu信息不能为空');
    }
    ZhiFuResult zhiFuResult = await ChannelManager().androidZhiFu(
      zhiFuType: ZhiFuType.alizhifu,
      orderId: orderId,
      tradeInfo: tradeInfo,
    );
    return ZhiFuResult.buildReturnResult(zhiFuResult.code, zhiFuResult.message);
  }
}
