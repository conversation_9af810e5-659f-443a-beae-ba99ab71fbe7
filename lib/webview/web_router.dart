import 'dart:io';

import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:dlyz_flutter/webview/webview.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';

import '../config/app_config.dart';
import '../utils/device_info_util.dart';
import '../providers/user_provider.dart';
import '../utils/enhanced_device_info_util.dart';

class WebRouter {
  static Future<void> jumpToWebPage(
    BuildContext context,
    String url, {
    String? title,
    Map<String, dynamic>? params,
  }) async {
    // 将 params 拼接到 URL 后面
    String finalUrl = await buildUrlWithParams(url, params ?? {}, context);
    LogUtil.d('jumpToWebPage url = $finalUrl');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewDialog(title: title ?? "", url: finalUrl),
      ),
    );
  }

  /// 将参数拼接到 URL 后面
  static Future<String> buildUrlWithParams(
    String url,
    Map<String, dynamic> params,
    BuildContext context,
  ) async {
    // 检查 URL 是否为空或无效
    if (url.isEmpty) {
      return url;
    }

    final commonParams = await _getWebCommonParams(context);
    final mergeParams = {...params, ...commonParams};

    try {
      // 解析现有的 URI
      Uri uri = Uri.parse(url);

      // 将 params 转换为查询参数
      Map<String, String> queryParams = {};

      // 保留原有的查询参数
      queryParams.addAll(uri.queryParameters);

      // 添加新的参数
      mergeParams.forEach((key, value) {
        if (value != null && key.isNotEmpty) {
          queryParams[key] = value.toString();
        }
      });

      // 重新构建 URI
      Uri newUri = uri.replace(queryParameters: queryParams);

      return newUri.toString();
    } catch (e) {
      // URI 解析失败时，尝试简单拼接参数
      print('URI 解析失败: $e');
      return url;
    }
  }

  static Future<Map<String, dynamic>> _getWebCommonParams(
    BuildContext context,
  ) async {
    final dev = await DeviceInfoUtil.getDev();
    final ticket = context.read<UserProvider>().currentTicket;
    final uid = context.read<UserProvider>().currentUser?.muid ?? '';
    final uname = context.read<UserProvider>().currentUser?.alias ?? '';
    final version = await EnhancedDeviceInfoUtil.getVersionName();
    Map<String, dynamic> params = {
      'appid': AppConfig.appId,
      'env': 'dl',
      'dev': dev,
      'gid': AppConfig.gid,
      'pid': AppConfig.pid,
      'gwversion': AppConfig.gwversion,
      'os': Platform.isAndroid ? 'android' : 'ios',
      'refer': AppConfig.refer,
      'scut': '0',
      'sversion': AppConfig.sversion,
      'app_ticket': ticket,
      'uid': uid,
      'uname': uname,
      'version': version,
    };
    if (Platform.isIOS) {
      final idfa = await DeviceInfoUtil.getIDFA();
      LogUtil.d('zls idfa = ${idfa}');
      params['idfa'] = idfa;
    }
    return params;
  }
}
