import 'package:dlyz_flutter/pages/games/GamesPage.dart';
import 'package:dlyz_flutter/track/game_center_track.dart';
import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../webview_bridge_interface.dart';

class JumpGameCenterPageHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "jumpGameCenterPage";

  JumpGameCenterPageHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) async {
    GameCenterTrack.trackGameCenterShow(GameCenterShowSourceType.cashClick);
    await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const GamesPage()),
    );
  }
}
