import 'dart:async';
import 'package:dlyz_flutter/events/event_bus_manager.dart';
import 'package:dlyz_flutter/pages/settings/AccountManagementPage.dart';
import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:dlyz_flutter/webview/web_router.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../webview_bridge_interface.dart';
import '../../providers/user_provider.dart';

class OpenAccountManagementHandler extends IWebViewBridge {
  static final String bridge_name = "openAccountManagement";
  StreamSubscription<UserDataChangedEvent>? _userDataSubscription;
  String? _originalUrl;

  OpenAccountManagementHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return bridge_name;
  }

  void _initEventListeners() {
    // 监听用户数据变更事件
    _userDataSubscription = EventBusManager.instance.on<UserDataChangedEvent>((event) async {
      print("账号管理Handler - 收到用户数据变更事件");
      
      // 如果有原始URL，则重新加载带有新app_ticket的URL
      if (_originalUrl != null && controller != null) {
        final appTicket = UserDataManager().currentTicket;
        if (appTicket != null && appTicket.isNotEmpty) {
          // 对app_ticket进行URL编码
          final encodedTicket = Uri.encodeComponent(appTicket);
          
          // 构建带有app_ticket参数的URL
          String updatedUrl;
          if (_originalUrl!.contains('?')) {
            // URL已有参数，添加&
            updatedUrl = '$_originalUrl&app_ticket=$encodedTicket';
          } else {
            // URL无参数，添加?
            updatedUrl = '$_originalUrl?app_ticket=$encodedTicket';
          }

          String finalUrl = await WebRouter.buildUrlWithParams(updatedUrl, {}, context);
          print("app_ticket $appTicket");
          print("updateUrl $finalUrl");
          // 重新加载URL
          controller!.loadUrl(finalUrl);
        }
      }
    });
  }



  @override
  void handler(JSMessage message) async {
    _initEventListeners();
    String? url = message.data["url"];
    if (url == null || url.isEmpty) {
      return;
    }
    print("receive url $url");
    // 保存原始URL供事件监听器使用
    _originalUrl = url;

    await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AccountManagementPage()),
    );
    dispose();

  }

  void dispose() {
    _userDataSubscription?.cancel();
  }
}