import 'dart:collection';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dlyz_flutter/net/config/http_base_config.dart';
import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/material.dart';
import '../../config/app_config.dart';
import '../../utils/device_info_util.dart';
import '../../utils/enhanced_device_info_util.dart';
import '../../utils/md5_utils.dart';
import '../../utils/sp_utils.dart';
import '../http_service.dart';
import '../http_base_response.dart';
import '../transformers/response_transformers.dart';
import '../sign_interceptor.dart';
import '../../model/m_layer_activate_response.dart';
import '../../model/s_layer_activate_response.dart';
import '../../model/server_info_response.dart';

class InitService {
  static final InitService _instance = InitService._internal();
  factory InitService() => _instance;
  InitService._internal();

  static DeviceInfoPlugin? _deviceInfoPlugin;
  static PackageInfo? _packageInfo;
  static AndroidDeviceInfo? _androidInfo;
  static IosDeviceInfo? _iosInfo;



  /// 初始化设备信息
  static Future<void> _initDeviceInfo() async {
    _deviceInfoPlugin ??= DeviceInfoPlugin();
    _packageInfo ??= await PackageInfo.fromPlatform();

    if (Platform.isAndroid) {
      _androidInfo ??= await _deviceInfoPlugin!.androidInfo;
    } else if (Platform.isIOS) {
      _iosInfo ??= await _deviceInfoPlugin!.iosInfo;
    }
  }


  /// 获取屏幕宽度像素
  static int _getWidthPixels(BuildContext? context) {
    if (context != null) {
      return EnhancedDeviceInfoUtil.getWidthPixels(context);
    }
    return 1080; // 默认值
  }

  /// 获取屏幕高度像素
  static int _getHeightPixels(BuildContext? context) {
    if (context != null) {
      return EnhancedDeviceInfoUtil.getHeightPixels(context);
    }
    return 1920; // 默认值
  }

  /// 获取设备型号
  static Future<String> _getDeviceModel() async {
    return await EnhancedDeviceInfoUtil.getDeviceModel();
  }

  /// 获取操作系统版本
  static Future<String> _getOSVersion() async {
    return await EnhancedDeviceInfoUtil.getOSVersion();
  }

  /// 获取操作系统描述
  static Future<String> _getOSDescription() async {
    return await EnhancedDeviceInfoUtil.getOSDescription();
  }

  /// 获取设备品牌
  static Future<String> _getDeviceBrand() async {
    return await EnhancedDeviceInfoUtil.getDeviceBrand();
  }

  /// 获取电话号码 (模拟)
  static String _getPhoneNumber() {
    // 由于隐私限制，返回空字符串
    return "";
  }

  /// 获取应用包名
  static Future<String> _getPackageName() async {
    return await EnhancedDeviceInfoUtil.getPackageName();
  }

  /// 获取网络类型 (简化版本)
  static String _getNetworkType() {
    // 简化版本，实际需要使用connectivity_plus插件
    return "wifi"; // 默认值
  }

  /// 获取版本号
  static Future<String> _getVersionCode() async {
    return await EnhancedDeviceInfoUtil.getVersionCode();
  }

  /// 获取电池电量 (模拟)
  static int _getBatteryLevel() {
    // 需要使用battery_plus插件
    return 100; // 默认值
  }

  /// 获取电池状态 (模拟)
  static int _getBatteryStatus() {
    // 需要使用battery_plus插件
    return 2; // 2表示不在充电
  }

  /// 获取WiFi SSID (模拟)
  static String _getWifiSSID() {
    // 需要使用wifi_info_flutter插件
    return "";
  }

  /// 获取WiFi BSSID (模拟)
  static String _getWifiBSSID() {
    // 需要使用wifi_info_flutter插件
    return "";
  }

  /// 获取插件版本号
  static String _getPluginVersion() {
    return "1.0.0"; // 默认插件版本
  }

  /// 检查是否为模拟器
  static Future<String> _getIsSimulator() async {
    bool isEmulator = await EnhancedDeviceInfoUtil.isEmulator();
    return isEmulator ? '1' : '0';
  }

  /// 构建公共参数
  static Future<Map<String, dynamic>> _buildCommonParams(
      {BuildContext? context}) async {
    await _initDeviceInfo();

    final now = DateTime
        .now()
        .millisecondsSinceEpoch ~/ 1000;

    Map<String, dynamic> commonParamsMap = {
      // 原有参数
      'dev': await DeviceInfoUtil.getDev(),
      'dev2': await DeviceInfoUtil.getDev(),
      'over': await _getOSVersion(),
      'gwversion': AppConfig.gwversion,
      'host_sdk_version': AppConfig.sversion,
      'gid': AppConfig.gid,
      'os': Platform.isAndroid ? 'android' : 'ios',
      'pid': AppConfig.pid,
      'is_root': '0',
      'version': AppConfig.sversion,
      'mac': DeviceInfoUtil.getMacAddress(),
      'is_simulator': await _getIsSimulator(),
      'refer': 'sy_00001',
      'sversion': AppConfig.sversion,
      'imei': DeviceInfoUtil.getImei(),
      'time': now.toString(),
      'android_id': await DeviceInfoUtil.getAndroidID(),

      // 新增Java代码中的参数
      'wpi': _getWidthPixels(context).toString(),
      'hpi': _getHeightPixels(context).toString(),
      'mode': await _getDeviceModel(),
      'os_desc': await _getOSDescription(),
      'brand': await _getDeviceBrand(),
      'phone': _getPhoneNumber(),
      'dpgn': await _getPackageName(),
      'nwk': _getNetworkType(),
      'sua': Platform.isAndroid ? '1' : '3', // 1:安卓, 2:iOS越狱, 3:正版iOS
      'versionCode': await _getVersionCode(),

      // 3.5.6 新增电量、WiFi信息
      'battery_level': _getBatteryLevel().toString(),
      'battery_status': _getBatteryStatus().toString(),
      'ssid': _getWifiSSID(),
      'bssid': _getWifiBSSID(),

      // 插件版本号
      'pluginVersion': _getPluginVersion(),
    };

    DeviceInfoUtil.setCommonParams(commonParamsMap);

    return commonParamsMap;
  }

  /// 获取公共参数 (对外接口)
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  static Future<Map<String, dynamic>> getCommonParams({
    BuildContext? context,
  }) async {
    return await _buildCommonParams(context: context);
  }

  /// M层激活接口
  /// SDK激活接口，用于激活SDK功能
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<MLayerActivateResponse>> mActivate({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 获取当前时间戳（毫秒）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      
      // 构建请求URL，包含时间戳参数
      final url = '/sdk/active/?t=$timestamp';
      
      // 获取公共参数
      final commonParams = await getCommonParams(context: context);
      
      // 合并额外参数
      if (additionalParams != null) {
        commonParams.addAll(additionalParams);
      }

      LogUtil.d('M层激活请求: $url, 参数: $commonParams');

      // 使用 HttpService 发送 POST 请求，指定 V5 签名
      final response = await HttpService.getInstance().post<MLayerActivateResponse>(
        url,
        baseUrl: HttpBaseConfig.mActivateBaseUrl,
        data: commonParams,
        contentType: ContentType.form,
        signType: SignType.v5,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return MLayerActivateResponse.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
        parseStrategy: ResponseTransformers.stateData<MLayerActivateResponse>(),
      );

      LogUtil.d('M层激活响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('M层激活请求失败: $e');
      return BaseResponse<MLayerActivateResponse>(
        code: 500,
        message: '激活请求失败: $e',
        data: null,
      );
    }
  }

  // ==================== S层激活接口 ====================

  /// S层激活接口
  /// S层SDK激活接口，返回完整的业务配置信息
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<SLayerActivateResponse>> sActivate({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 获取当前时间戳（毫秒）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      
      // 构建请求URL，包含时间戳参数
      final url = '/sdk/active/?t=$timestamp';
      
      // 获取公共参数
      final commonParams = await getCommonParams(context: context);
      
      // 合并额外参数
      if (additionalParams != null) {
        commonParams.addAll(additionalParams);
      }

      LogUtil.d('S层激活请求: $url, 参数: $commonParams');

      // 使用 HttpService 发送 POST 请求，指定 V2 签名
      final response = await HttpService.getInstance().post<SLayerActivateResponse>(
        url,
        baseUrl: HttpBaseConfig.sActivateBaseUrl,
        data: commonParams,
        contentType: ContentType.form,
        signType: SignType.v2,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return SLayerActivateResponse.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
        parseStrategy: ResponseTransformers.stateData<SLayerActivateResponse>(),
      );

      LogUtil.d('S层激活响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('S层激活请求失败: $e');
      return BaseResponse<SLayerActivateResponse>(
        code: 500,
        message: 'S层激活请求失败: $e',
        data: null,
      );
    }
  }

  // ==================== reportMDev接口 ====================

  /// M层设备上报接口
  /// 用于上报设备信息到服务器
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值  
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<Map<String, dynamic>>> reportMDev({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 获取设备信息
      final oaidMap = await DeviceInfoUtil.getOAIDMap();
      final deviceInfo = await DeviceInfoUtil.getDeviceInfo();
      
      // 获取当前时间戳（秒）
      final time = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      // 构建参数 TreeMap (按字典序排列)
      final treeMap = SplayTreeMap<String, dynamic>();
      treeMap['dev'] = deviceInfo['dev'] ?? '';
      treeMap['gid'] = int.parse(AppConfig.gid);
      // jsonEncode(oaidMap)
      treeMap['identify'] = oaidMap;
      treeMap['pid'] = int.parse(AppConfig.pid);
      treeMap['refer'] = AppConfig.refer;
      treeMap['sversion'] = AppConfig.sversion;
      treeMap['time'] = time.toString();
      treeMap['version'] = _packageInfo?.version ?? '';
      
      // 构建原始签名字符串
      final paramJson = jsonEncode(treeMap);
      final appKey = AppConfig.appKey;
      final originSignStr = paramJson + appKey;
      
      LogUtil.d('reportMDev 原始签名字符串: $originSignStr');
      
      // 计算MD5签名
      final sign = Md5Utils.generateMd5(originSignStr);
      
      // 构建最终请求参数
      final finalMap = Map<String, dynamic>.from(treeMap);
      finalMap['sign'] = sign;
      
      LogUtil.d('reportMDev 请求参数: $finalMap');

      // 使用 HttpService 发送 POST 请求，不使用额外签名（因为已经手动计算了sign）
      final response = await HttpService.getInstance().post<Map<String, dynamic>>(
        HttpBaseConfig.reportMDev,
        baseUrl: HttpBaseConfig.baseUrl,
        data: finalMap,
        contentType: ContentType.json,
        signType: SignType.none, // 不使用额外签名，因为已经手动计算了sign
        fromJsonT: (data) {
          if (data == null) {
            return <String, dynamic>{};
          }
          if (data is Map<String, dynamic>) {
            return data;
          }
          return <String, dynamic>{'response': data};
        },
        parseStrategy: ResponseTransformers.stateData<Map<String, dynamic>>(),
      );

      LogUtil.d('M层设备上报响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('M层设备上报请求失败: $e');
      return BaseResponse<Map<String, dynamic>>(
        code: 500,
        message: '设备上报请求失败: $e',
        data: null,
      );
    }
  }

  // ==================== server-info-service接口 ====================

  /// 获取服务器信息接口
  /// 获取游戏服务器URL配置信息
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<ServerInfoResponse>> getServerInfo({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      // 使用 HttpService 发送 GET 请求，指定 V2 签名
      final response = await HttpService.getInstance().get<ServerInfoResponse>(
        '/server-info-service/get-url',
        baseUrl: HttpBaseConfig.baseUrl,
        queryParameters: queryParams,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return ServerInfoResponse.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
      );

      LogUtil.d('获取服务器信息响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('获取服务器信息请求失败: $e');
      return BaseResponse.error("获取服务器信息请求失败");
    }
  }
}