import 'package:dlyz_flutter/utils/log_util.dart';

import '../http_service.dart';
import '../http_base_response.dart';
import '../sign_interceptor.dart';
import '../transformers/response_transformers.dart';
import '../../model/forum_category.dart';
import '../../model/forum_post_list.dart';
import '../../model/forum_config.dart';
import '../../model/topic_list.dart';
import '../../model/forum_login_info.dart';
import '../../model/vote_response.dart';
import '../../model/follow_response.dart';
import '../../providers/game_circle_provider.dart';
import '../../manager/forum_data_manager.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ForumService {
  static final ForumService _instance = ForumService._internal();
  factory ForumService() => _instance;
  ForumService._internal();

  final HttpService _httpService = HttpService();

  /// 获取带有当前游戏圈子tgid的动态headers
  static Map<String, dynamic> _getDynamicForumHeaders(BuildContext? context) {
    String tgid = ''; // 默认值
    
    if (context != null) {
      try {
        final gameCircleProvider = Provider.of<GameCircleProvider>(context, listen: false);
        if (gameCircleProvider.hasSelectedGameCircle && 
            gameCircleProvider.selectedGameCircle != null) {
          tgid = gameCircleProvider.selectedGameCircle!.tgid;
        }
      } catch (e) {
        // 如果获取失败，使用默认值
        print('获取当前游戏圈子tgid失败，使用默认值: $e');
      }
    }
    
    // 获取当前的授权信息
    final forumDataManager = ForumDataManager();
    final authHeader = forumDataManager.authorizationHeader ?? '';

    return {
      'authorization': authHeader,
      'forum-tgid': tgid,
    };
  }

  /// 获取内容分类
  Future<BaseResponse<List<ForumCategory>>> getCategories({
    required String baseUrl,
    BuildContext? context,
  }) async {
    return await _httpService.get<List<ForumCategory>>(
      '/categories',
      baseUrl: baseUrl,
      headers: _getDynamicForumHeaders(context),
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<List<ForumCategory>>(),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          return <ForumCategory>[];
        }
        // 直接解析 Data 字段中的数组
        if (data is List) {
          return data
              .map((item) => ForumCategory.fromJson(item))
              .toList();
        }
        return <ForumCategory>[];
      },
    );
  }

  /// 获取帖子列表
  Future<BaseResponse<ForumPostList>> getPostList({
    required String baseUrl,
    int? categoryId,
    String? categoryName,
    int page = 1,
    int pageSize = 5,
    int? scope,
    String? filterSearch,
    BuildContext? context,
    int? stype, // 添加stype参数
  }) async {
    // 根据分类名称设置scope参数：发现分类使用scope=1，其他分类使用scope=0
    int finalScope = stype == 3 ? 1 : 0;
    if(filterSearch != null && filterSearch.isNotEmpty){
      finalScope = 2;
    }
    final int attention = stype == 2 ? 1 : 0;
    final queryParams = <String, dynamic>{
      'page': page,
      'perPage': pageSize,
      'scope': finalScope,
      'filter[attention]': attention,
      'filter[sort]': 1,
    };
    
    if (categoryId != null && stype != 1 && stype != 2) {
      queryParams['filter[categoryids][0]'] = categoryId;
    }

    if (filterSearch != null && filterSearch.isNotEmpty) {
      queryParams['filter[search]'] = filterSearch;
    }

    LogUtil.d("分类:${categoryId} stype:${stype}");

    // 如果stype不为null且不为1，则添加到查询参数中
    if (stype != null && stype != 1) {
      queryParams['stype'] = stype;
    }

    return await _httpService.get<ForumPostList>(
      '/thread.list',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _getDynamicForumHeaders(context),
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<ForumPostList>(),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          throw Exception('帖子列表数据为空');
        }
        // 直接解析 Data 字段中的对象
        return ForumPostList.fromJson(data);
      },
    );
  }

  /// 获取帖子详情
  Future<BaseResponse<ForumPost>> getPostDetail({
    required String baseUrl,
    required int threadId,
    BuildContext? context,
  }) async {
    return await _httpService.get<ForumPost>(
      '/thread.detail',
      baseUrl: baseUrl,
      queryParameters: {'threadId': threadId},
      headers: _getDynamicForumHeaders(context),
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<ForumPost>(),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          throw Exception('帖子详情数据为空');
        }
        // 直接解析 Data 字段中的对象
        return ForumPost.fromJson(data);
      },
    );
  }

  /// 获取论坛配置信息
  Future<BaseResponse<ForumConfig>> getForumConfig({
    required String baseUrl,
    BuildContext? context,
  }) async {
    return await _httpService.get<ForumConfig>(
      '/forum',
      baseUrl: baseUrl,
      headers: _getDynamicForumHeaders(context),
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<ForumConfig>(),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          throw Exception('论坛配置数据为空');
        }
        // 直接解析 Data 字段中的对象
        return ForumConfig.fromJson(data);
      },
    );
  }

  /// 获取分类对应的推荐/活动/话题配置
  /// 返回 Data 为数组，每个元素包含：style_name/title/content/count 等
  Future<BaseResponse<List<Map<String, dynamic>>>> getActLabel({
    required String baseUrl,
    int? categoryId,
    BuildContext? context,
  }) async {
    final queryParams = <String, dynamic>{
      'category_id': categoryId,
    };

    return await _httpService.get<List<Map<String, dynamic>>>(
      '/actlabelpage/query',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _getDynamicForumHeaders(context),
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<List<Map<String, dynamic>>>(),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          return <Map<String, dynamic>>[];
        }
        if (data is List) {
          return data.map<Map<String, dynamic>>((e) => Map<String, dynamic>.from(e as Map)).toList();
        }
        return <Map<String, dynamic>>[];
      },
    );
  }

  /// 获取集合对应的推荐/活动/话题配置
  /// 返回 Data 为数组，每个元素包含：style_name/title/content/count 等
  Future<BaseResponse<List<Map<String, dynamic>>>> getCollection({
    required String baseUrl,
    int? collectionId,
    BuildContext? context,
  }) async {
    final queryParams = <String, dynamic>{
      'collection_id': collectionId,
    };

    return await _httpService.get<List<Map<String, dynamic>>>(
      '/actlabelpage/query',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _getDynamicForumHeaders(context),
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<List<Map<String, dynamic>>>(),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          return <Map<String, dynamic>>[];
        }
        if (data is List) {
          return data.map<Map<String, dynamic>>((e) => Map<String, dynamic>.from(e as Map)).toList();
        }
        return <Map<String, dynamic>>[];
      },
    );
  }

  /// 举报帖子
  /// 对应接口: POST reports
  /// 自动使用论坛登录用户的userId进行举报
  /// 请求体示例见 `lib/api2`，返回体示例见 `lib/api`
  Future<BaseResponse<Map<String, dynamic>>> reportThread({
    required String baseUrl,
    required int threadId,
    required String reason,
    int type = 1,
    BuildContext? context,
  }) async {
    // 如果没有提供userId，从论坛登录数据中获取
    int finalUserId = getCurrentForumUserId() ?? 0;
    
    // 检查是否有有效的用户ID
    if (finalUserId == 0) {
      throw Exception('用户未登录论坛或用户ID无效，无法进行举报操作');
    }
    
    final Map<String, dynamic> body = {
      'reason': reason,
      'userId': finalUserId,
      'type': type,
      'threadId': threadId,
    };

    return await _httpService.post<Map<String, dynamic>>(
      '/reports',
      baseUrl: baseUrl,
      data: body,
      signType: SignType.v3,
      headers: _getDynamicForumHeaders(context),
      parseStrategy: ResponseTransformers.codeData<Map<String, dynamic>>(successCode: 0),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          return <String, dynamic>{};
        }
        // 返回 Data 字段对象，例如 {"id": 3061}
        if (data is Map<String, dynamic>) {
          return Map<String, dynamic>.from(data);
        }
        return <String, dynamic>{};
      },
    );
  }

  /// 获取话题列表
  Future<BaseResponse<TopicList>> getTopicList({
    required String baseUrl,
    required int topicId,
    int hot = 0,
    int page = 1,
    int perPage = 10,
    BuildContext? context,
  }) async {
    final queryParams = <String, dynamic>{
      'filter[topicId]': topicId,
      'filter[hot]': hot,
      'perPage': perPage,
      'page': page,
    };

    return await _httpService.get<TopicList>(
      '/topics.list',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _getDynamicForumHeaders(context),
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<TopicList>(),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          throw Exception('话题列表数据为空');
        }
        // 直接解析 Data 字段中的对象
        return TopicList.fromJson(data);
      },
    );
  }

  /// 论坛登录接口
  /// 接口路径: ptbind
  /// 使用v3签名
  Future<BaseResponse<ForumLoginInfo>> forumLogin({
    required String baseUrl,
    required Map<String, dynamic> loginData,
    BuildContext? context,
  }) async {
    final response = await _httpService.post<ForumLoginInfo>(
      '/ptbind',
      baseUrl: baseUrl,
      data: loginData,
      headers: _getDynamicForumHeaders(context),
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<ForumLoginInfo>(),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          throw Exception('登录信息数据为空');
        }
        // 直接解析 Data 字段中的对象
        return ForumLoginInfo.fromJson(data);
      },
    );
    // 如果登录成功，保存登录数据到全局管理器
    if (response.isSuccess && response.data != null) {
      final forumDataManager = ForumDataManager();
      await forumDataManager.saveForumLoginInfo(response.data!);
      LogUtil.d('论坛登录成功，数据已保存到全局管理器', tag: 'ForumService');
    }

    LogUtil.d('论坛登录${response.isSuccess} code:${response.code}');
    return response;
  }

  /// 从全局管理器初始化授权信息
  /// 应在应用启动时调用
  static Future<void> initializeFromGlobalData() async {
    final forumDataManager = ForumDataManager();
    await forumDataManager.loadForumLoginInfo();
  }

  /// 清除论坛登录状态
  /// 包括全局管理器和本地authorization
  static Future<void> clearForumLoginState() async {
    final forumDataManager = ForumDataManager();
    await forumDataManager.clearForumLoginInfo();
    LogUtil.d('论坛登录状态已清除', tag: 'ForumService');
  }

  /// 检查当前是否已登录论坛
  static bool isForumLoggedIn() {
    final forumDataManager = ForumDataManager();
    return forumDataManager.isForumLoggedIn;
  }

  /// 获取当前论坛用户ID
  static int? getCurrentForumUserId() {
    final forumDataManager = ForumDataManager();
    return forumDataManager.currentUserId;
  }

  /// 获取当前论坛登录信息
  static ForumLoginInfo? getCurrentForumLoginInfo() {
    final forumDataManager = ForumDataManager();
    return forumDataManager.currentForumLoginInfo;
  }

  /// 投票接口
  /// 对应接口: POST /vote/add
  /// 请求参数: vote_id (String), items (String, 格式: [id1,id2])
  Future<BaseResponse<VoteResponse>> submitVote({
    required String baseUrl,
    required String voteId,
    required String items,
    BuildContext? context,
  }) async {
    final Map<String, dynamic> body = {
      'vote_id': voteId,
      'items': items,
    };

    return await _httpService.post<VoteResponse>(
      '/vote/add',
      baseUrl: baseUrl,
      data: body,
      signType: SignType.v3,
      headers: _getDynamicForumHeaders(context),
      parseStrategy: ResponseTransformers.codeData<VoteResponse>(successCode: 0),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          throw Exception('投票响应数据为空');
        }
        // 直接解析 Data 字段中的对象
        return VoteResponse.fromJson(data);
      },
    );
  }

  /// 累加帖子浏览量接口
  /// 对应接口: GET /view.count
  /// 请求参数: threadId (int)
  Future<BaseResponse<Map<String, dynamic>>> incrementViewCount({
    required String baseUrl,
    required int threadId,
    BuildContext? context,
  }) async {
    return await _httpService.get<Map<String, dynamic>>(
      '/view.count',
      baseUrl: baseUrl,
      queryParameters: {'threadId': threadId},
      headers: _getDynamicForumHeaders(context),
      signType: SignType.v3,
      parseStrategy: ResponseTransformers.codeData<Map<String, dynamic>>(),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          return <String, dynamic>{};
        }
        // 直接解析 Data 字段中的对象
        if (data is Map<String, dynamic>) {
          return Map<String, dynamic>.from(data);
        }
        return <String, dynamic>{};
      },
    );
  }

  /// 关注用户接口
  /// 对应接口: POST /follow
  /// 请求参数: toUserId (int) - 被关注用户的ID
  Future<BaseResponse<FollowResponse>> followUser({
    required String baseUrl,
    required int toUserId,
    BuildContext? context,
  }) async {
    final Map<String, dynamic> body = {
      'toUserId': toUserId,
    };

    return await _httpService.post<FollowResponse>(
      '/follow.create',
      baseUrl: baseUrl,
      data: body,
      signType: SignType.v3,
      headers: _getDynamicForumHeaders(context),
      parseStrategy: ResponseTransformers.codeData<FollowResponse>(successCode: 0),
      fromJsonT: (data) {
        // 检查data是否为null
        if (data == null) {
          // 如果data为null，返回一个空的FollowResponse对象
          return FollowResponse();
        }
        // 直接解析 Data 字段中的对象
        return FollowResponse.fromJson(data);
      },
    );
  }
}

