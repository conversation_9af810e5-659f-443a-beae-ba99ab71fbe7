import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/material.dart';
import 'package:dlyz_flutter/config/app_config.dart';
import 'package:dlyz_flutter/net/sign_interceptor.dart';
import 'package:dlyz_flutter/providers/user_provider.dart';
import 'package:dlyz_flutter/net/interceptors/dio_risk_interceptor.dart';
import 'package:dlyz_flutter/net/interceptors/login_validation_interceptor.dart';
import 'gateway_encrypt_interceptor.dart';
import 'http_base_response.dart';
import 'http_error_code.dart';
import 'http_net_exception.dart';
import 'transformers/response_parse_strategy.dart';
import 'transformers/response_transformers.dart';
import '../utils/log_util.dart';
import 'config/proxy_config.dart';
import 'config/http_base_config.dart';
import 'common_params_interceptor.dart';
import '../events/event_bus_manager.dart';
import '../events/login_invalid_event.dart';
import 'dart:convert';

/// HTTP Content-Type 枚举
enum ContentType {
  json('application/json'),
  form('application/x-www-form-urlencoded'),
  multipart('multipart/form-data'),
  xml('application/xml'),
  text('text/plain'),
  html('text/html'),
  octetStream('application/octet-stream');

  const ContentType(this.value);
  final String value;
}

class HttpService {
  static final HttpService _instance = HttpService._internal();

  factory HttpService() => _instance;

  final Map<String, Dio> _dioCache = {};
  UserProvider? _userProvider;
  GlobalKey<NavigatorState>? _navigatorKey;

  HttpService._internal();

  static HttpService getInstance() {
    return _instance;
  }


  /// 设置NavigatorKey用于风控拦截器
  void setNavigatorKey(GlobalKey<NavigatorState> navigatorKey) {
    _navigatorKey = navigatorKey;
    // 清空缓存，以便重新创建带有新NavigatorKey的Dio实例
    _dioCache.clear();
  }

  Dio _getDio(String baseUrl, {ContentType? contentType}) {
    // 使用baseUrl和contentType作为缓存key
    final cacheKey = contentType != null ? '$baseUrl-${contentType.value}' : baseUrl;
    
    if (_dioCache.containsKey(cacheKey)) return _dioCache[cacheKey]!;

    final dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        responseType: ResponseType.plain,
        headers: {
          'Content-Type': contentType?.value ?? ContentType.json.value,
        },
      ),
    );
    final adapter = IOHttpClientAdapter();
    adapter.createHttpClient = () {
      final client = HttpClient();
      client.findProxy = (uri) {
        return ProxyConfig.getInstance().getProxyString();
      };
      client.badCertificateCallback =
          (X509Certificate cert, String host, int port) => ProxyConfig.getInstance().shouldTrustCertificate();
      return client;
    };
    dio.httpClientAdapter = adapter;
    dio.interceptors.add(CommonParamsInterceptor());
    dio.interceptors.add(LogInterceptor(responseBody: true));
    dio.interceptors.add(SignInterceptor(appKey: AppConfig.appKey));
    
    // 添加风控拦截器
    if (_navigatorKey != null) {
      dio.interceptors.add(RiskInterceptor(dio, _navigatorKey!));
    }
    // 添加登录校验拦截器
    dio.interceptors.add(LoginValidationInterceptor());

    // 根据当前URL是否包含secureSuffix决定是否添加加密拦截器
    if (baseUrl.contains(HttpBaseConfig.secureSuffix) && HttpBaseConfig.secureSuffix.isNotEmpty) {
      dio.interceptors.add(GateWayEncryptInterceptor(provider: Game37GateWayProvider(
        key: 'soC2GAr8jN2fsbry',
        version: '',
      )));
    }
    
    _dioCache[cacheKey] = dio;
    return dio;
  }

  Future<BaseResponse<T>> get<T>(
    String path, {
    required String baseUrl,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    ContentType? contentType,
    required T Function(dynamic) fromJsonT,
    ResponseParseStrategy<T>? parseStrategy,
    SignType signType = SignType.none,
  }) async {
    final stopwatch = Stopwatch()..start();
    final fullUrl = '$baseUrl$path';
    
    // 记录请求日志
    LogUtil.request(
      fullUrl,
      method: 'GET',
      params: queryParameters,
      headers: headers,
    );

    try {
      // 合并自定义headers和Content-Type
      final mergedHeaders = <String, dynamic>{};
      if (contentType != null) {
        mergedHeaders['Content-Type'] = contentType.value;
      }
      if (headers != null) {
        mergedHeaders.addAll(headers);
      }
      mergedHeaders['signType'] = signType;

      final response = await _getDio(
        baseUrl,
        contentType: contentType,
      ).get(
        path, 
        queryParameters: queryParameters,
        options: mergedHeaders.isNotEmpty ? Options(headers: mergedHeaders) : null,
      );
      
      stopwatch.stop();
      
      // 记录响应日志
      LogUtil.response(
        fullUrl,
        response.statusCode ?? 0,
        response.data,
        method: 'GET',
        duration: stopwatch.elapsedMilliseconds,
        maxDataLength: 2000, // 限制响应数据长度为2000字符
      );
      
      return _handleResponse(response, fromJsonT, parseStrategy);
    } on DioException catch (e) {
      stopwatch.stop();
      
      // 记录错误响应日志
      LogUtil.response(
        fullUrl,
        e.response?.statusCode ?? 0,
        e.response?.data ?? e.message,
        method: 'GET',
        duration: stopwatch.elapsedMilliseconds,
        maxDataLength: 2000, // 限制响应数据长度为2000字符
      );
      
      LogUtil.e('GET请求失败: $fullUrl', error: e);
      return handleDioError<T>(e);
    } catch (e) {
      stopwatch.stop();
      LogUtil.e('GET请求异常: $fullUrl', error: e);
      return BaseResponse.error('未知错误: $e', code: ErrorCode.unknown);
    }
  }

  Future<BaseResponse<T>> post<T>(
    String path, {
    required String baseUrl,
    dynamic data, // 改为dynamic以支持不同数据类型
    Map<String, dynamic>? headers,
    ContentType? contentType,
    required T Function(dynamic) fromJsonT,
    ResponseParseStrategy<T>? parseStrategy,
    SignType signType = SignType.none,
  }) async {
    final stopwatch = Stopwatch()..start();
    final fullUrl = '$baseUrl$path';
    
    // 记录请求日志
    LogUtil.request(
      fullUrl,
      method: 'POST',
      params: data,
      headers: headers,
    );

    try {
      // 合并自定义headers和Content-Type
      final mergedHeaders = <String, dynamic>{};
      if (contentType != null) {
        mergedHeaders['Content-Type'] = contentType.value;
      }
      if (headers != null) {
        mergedHeaders.addAll(headers);
      }
      mergedHeaders['signType'] = signType;

      // 根据Content-Type处理数据格式
      final processedData = _processRequestData(data, contentType);

      final response = await _getDio(
        baseUrl, 
        contentType: contentType,
      ).post(
        path, 
        data: processedData,
        options: mergedHeaders.isNotEmpty ? Options(headers: mergedHeaders) : null,
      );
      
      stopwatch.stop();
      
      // 记录响应日志
      LogUtil.response(
        fullUrl,
        response.statusCode ?? 0,
        response.data,
        method: 'POST',
        duration: stopwatch.elapsedMilliseconds,
        maxDataLength: 2000, // 限制响应数据长度为2000字符
      );
      
      return _handleResponse(response, fromJsonT, parseStrategy);
    } on DioException catch (e) {
      stopwatch.stop();
      
      // 记录错误响应日志
      LogUtil.response(
        fullUrl,
        e.response?.statusCode ?? 0,
        e.response?.data ?? e.message,
        method: 'POST',
        duration: stopwatch.elapsedMilliseconds,
        maxDataLength: 2000, // 限制响应数据长度为2000字符
      );
      
      LogUtil.e('POST请求失败: $fullUrl', error: e);
      return handleDioError<T>(e);
    } catch (e) {
      stopwatch.stop();
      LogUtil.e('POST请求异常: $fullUrl', error: e);
      return BaseResponse.error('未知错误: $e', code: ErrorCode.unknown);
    }
  }


  /// 处理请求数据，根据Content-Type进行格式转换
  dynamic _processRequestData(dynamic data, ContentType? contentType) {
    if (data == null) return null;
    
    switch (contentType) {
      case ContentType.form:
        // 表单编码格式
        if (data is Map<String, dynamic>) {
          return data.entries
              .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
              .join('&');
        }
        return data;
        
      case ContentType.json:
        // JSON格式，保持原样，Dio会自动序列化
        return data;
        
      case ContentType.multipart:
        // FormData格式
        if (data is Map<String, dynamic>) {
          final formData = FormData();
          for (final entry in data.entries) {
            if (entry.value is MultipartFile) {
              formData.files.add(MapEntry(entry.key, entry.value));
            } else {
              formData.fields.add(MapEntry(entry.key, entry.value.toString()));
            }
          }
          return formData;
        } else if (data is FormData) {
          return data;
        }
        return data;
        
      case ContentType.xml:
      case ContentType.text:
      case ContentType.html:
        // 文本格式，转换为字符串
        return data.toString();
        
      case ContentType.octetStream:
        // 二进制数据，保持原样
        return data;
        
      default:
        // 默认情况，保持原样
        return data;
    }
  }

  BaseResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic json) fromJsonT,
    ResponseParseStrategy<T>? parseStrategy,
  ) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      try {
        BaseResponse<T> result;
        
        // 如果提供了解析策略，使用策略解析
        if (parseStrategy != null) {
          result = parseStrategy.parse(response.data, fromJsonT);
        } else {
          // 默认使用 state/msg/data 格式策略
          final defaultStrategy = ResponseTransformers.stateData<T>();
          result = defaultStrategy.parse(response.data, fromJsonT);
        }
        
        // 检查登录失效错误码
        _checkLoginInvalidation(result, response);
        
        return result;
      } catch (e) {
        LogUtil.e('数据解析失败', error: e);
        return BaseResponse.error('解析失败: $e', code: ErrorCode.parseError);
      }
    } else {
      LogUtil.w('HTTP状态码异常: ${response.statusCode}');
      return BaseResponse.error(
        'HTTP错误: ${response.statusCode}',
        code: ErrorCode.httpError,
      );
    }
  }
  
  /// 检查登录失效情况
  void _checkLoginInvalidation<T>(BaseResponse<T> result, Response response) {
    // 检查响应码是否为登录失效
    if (result.code == -4011 || result.code == -3001) {
      LogUtil.w('检测到登录失效，错误码: ${result.code}，消息: ${result.message}');
      
      // 获取API路径
      String? apiPath;
      try {
        final requestOptions = response.requestOptions;
        apiPath = '${requestOptions.baseUrl}${requestOptions.path}';
      } catch (e) {
        LogUtil.w('获取API路径失败: $e');
      }
      
      // 发送登录失效事件
      final event = LoginInvalidEvent(
        code: result.code,
        message: result.message,
        apiPath: apiPath,
      );
      
      EventBusManager.instance.fire(event);
      LogUtil.d('已发送登录失效事件: $event');
    }
  }
}
