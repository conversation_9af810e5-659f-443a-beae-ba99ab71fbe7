import 'response_parse_strategy.dart';
import '../http_base_response.dart';
import '../http_error_code.dart';
import '../../utils/log_util.dart';

/// Code/Message/Data 格式解析策略
class CodeMessageDataStrategy<T> extends ResponseParseStrategy<T> {
  final int successCode;

  CodeMessageDataStrategy({this.successCode = 200});

  @override
  BaseResponse<T> parseJsonData(dynamic jsonData, T Function(dynamic) fromJsonT) {
    if (jsonData is Map<String, dynamic>) {
      final code = jsonData['code'] ?? jsonData['Code'] ?? successCode;
      final message = jsonData['message'] ?? jsonData['Message'] ?? 'OK';
      final data = jsonData['data'] ?? jsonData['Data'];

      if (code == successCode) {
        // 成功状态
        final parsedData = fromJsonT(data);
        return BaseResponse<T>(
          code: 200,
          message: message.toString(),
          data: parsedData,
        );
      } else {
        // 失败状态
        LogUtil.w('业务逻辑失败: code=$code, message=$message');
        return BaseResponse.error(
          message.toString(),
          code: code,
        );
      }
    } else {
      LogUtil.e('响应格式不正确，期望Map<String, dynamic>，实际: ${jsonData.runtimeType}');
      return BaseResponse.error(
        '响应格式错误',
        code: ErrorCode.parseError,
      );
    }
  }

  @override
  String get strategyName => 'CodeMessageDataStrategy';
}