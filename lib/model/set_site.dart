import 'platform_setting.dart';

/// 网站设置
class SetSite {
  final List<dynamic> siteManage;
  final String? apiFreq;
  final String siteUrl;
  final String siteInstall;
  final String siteCover;
  final String siteMinimumAmount;
  final List<bool> loginaes;
  final List<String> aesKey;
  final List<String> aesIv;
  final bool usernameLoginIsdisplay;
  final String openApiLog;
  final int threadTab;
  final String version;
  final String siteName;
  final String siteTitle;
  final int siteFabu;
  final String siteKeywords;
  final String siteIntroduction;
  final String siteMode;
  final String openExtFields;
  final bool siteClose;
  final String siteCloseMsg;
  final String siteFavicon;
  final String siteLogo;
  final String siteHeaderLogo;
  final String siteBackgroundImage;
  final String siteUserBackgroundImage;
  final int siteShareSwitch;
  final String siteSearchSwitch;
  final String siteSignInSwitch;
  final int siteSpaceCollectionPageSwitch;
  final String siteAuthSwitch;
  final String siteLoginType;
  final String sitePcMixThreadSwitch;
  final String siteWechatLoginPid;
  final String siteWechatLoginGid;
  final String siteSignInUrl;
  final String siteSystemMaintenance;
  final String siteActEntranceSwitch;
  final String siteActEntranceUrl;
  final String siteStat;
  final String? siteAuthor;
  final String siteRecord;
  final String siteRecordCode;
  final String siteMasterScale;
  final int siteOpenSort;
  final String openViewCount;
  final int siteTitles;
  final List<PlatformSetting> siteRewards;
  final List<PlatformSetting> siteAreward;
  final List<PlatformSetting> siteRedpacket;
  final List<PlatformSetting> siteAnonymous;
  final List<PlatformSetting> sitePersonalletter;
  final List<PlatformSetting> siteShop;
  final List<PlatformSetting> siteUsergroup;
  final List<PlatformSetting> siteWithdrawal;
  final List<PlatformSetting> siteComment;

  SetSite({
    required this.siteManage,
    this.apiFreq,
    required this.siteUrl,
    required this.siteInstall,
    required this.siteCover,
    required this.siteMinimumAmount,
    required this.loginaes,
    required this.aesKey,
    required this.aesIv,
    required this.usernameLoginIsdisplay,
    required this.openApiLog,
    required this.threadTab,
    required this.version,
    required this.siteName,
    required this.siteTitle,
    required this.siteFabu,
    required this.siteKeywords,
    required this.siteIntroduction,
    required this.siteMode,
    required this.openExtFields,
    required this.siteClose,
    required this.siteCloseMsg,
    required this.siteFavicon,
    required this.siteLogo,
    required this.siteHeaderLogo,
    required this.siteBackgroundImage,
    required this.siteUserBackgroundImage,
    required this.siteShareSwitch,
    required this.siteSearchSwitch,
    required this.siteSignInSwitch,
    required this.siteSpaceCollectionPageSwitch,
    required this.siteAuthSwitch,
    required this.siteLoginType,
    required this.sitePcMixThreadSwitch,
    required this.siteWechatLoginPid,
    required this.siteWechatLoginGid,
    required this.siteSignInUrl,
    required this.siteSystemMaintenance,
    required this.siteActEntranceSwitch,
    required this.siteActEntranceUrl,
    required this.siteStat,
    this.siteAuthor,
    required this.siteRecord,
    required this.siteRecordCode,
    required this.siteMasterScale,
    required this.siteOpenSort,
    required this.openViewCount,
    required this.siteTitles,
    required this.siteRewards,
    required this.siteAreward,
    required this.siteRedpacket,
    required this.siteAnonymous,
    required this.sitePersonalletter,
    required this.siteShop,
    required this.siteUsergroup,
    required this.siteWithdrawal,
    required this.siteComment,
  });

  factory SetSite.fromJson(Map<String, dynamic> json) {
    // 安全地获取值，处理类型转换
    T? _safeGet<T>(String key) {
      final value = json[key];
      if (value == null) return null;
      
      // 如果已经是目标类型，直接返回
      if (value is T) return value;
      
      // 特殊处理不同类型的转换
      if (T == int) {
        if (value is String) return int.tryParse(value) as T?;
        if (value is double) return value.toInt() as T?;
      } else if (T == bool) {
        if (value is String) {
          return (value.toLowerCase() == 'true' || value == '1') as T?;
        }
        if (value is int) return (value == 1) as T?;
      } else if (T == String) {
        return value.toString() as T?;
      }
      
      return null;
    }

    Map<String, dynamic> _safeMapCast(dynamic value) {
      if (value == null) return <String, dynamic>{};
      if (value is Map<String, dynamic>) return value;
      if (value is Map) {
        return Map<String, dynamic>.from(value);
      }
      return <String, dynamic>{};
    }

    return SetSite(
      siteManage: json['siteManage'] ?? [],
      apiFreq: _safeGet<String>('apiFreq'),
      siteUrl: _safeGet<String>('siteUrl') ?? '',
      siteInstall: _safeGet<String>('siteInstall') ?? '',
      siteCover: _safeGet<String>('siteCover') ?? '',
      siteMinimumAmount: _safeGet<String>('siteMinimumAmount') ?? '',
      loginaes: List<bool>.from(json['loginaes'] ?? []),
      aesKey: List<String>.from(json['aesKey'] ?? []),
      aesIv: List<String>.from(json['aesIv'] ?? []),
      usernameLoginIsdisplay: _safeGet<bool>('usernameLoginIsdisplay') ?? false,
      openApiLog: _safeGet<String>('openApiLog') ?? '0',
      threadTab: _safeGet<int>('threadTab') ?? 0,
      version: _safeGet<String>('version') ?? '',
      siteName: _safeGet<String>('siteName') ?? '',
      siteTitle: _safeGet<String>('siteTitle') ?? '',
      siteFabu: _safeGet<int>('siteFabu') ?? 0,
      siteKeywords: _safeGet<String>('siteKeywords') ?? '',
      siteIntroduction: _safeGet<String>('siteIntroduction') ?? '',
      siteMode: _safeGet<String>('siteMode') ?? '',
      openExtFields: _safeGet<String>('openExtFields') ?? '',
      siteClose: _safeGet<bool>('siteClose') ?? false,
      siteCloseMsg: _safeGet<String>('siteCloseMsg') ?? '',
      siteFavicon: _safeGet<String>('siteFavicon') ?? '',
      siteLogo: _safeGet<String>('siteLogo') ?? '',
      siteHeaderLogo: _safeGet<String>('siteHeaderLogo') ?? '',
      siteBackgroundImage: _safeGet<String>('siteBackgroundImage') ?? '',
      siteUserBackgroundImage: _safeGet<String>('siteUserBackgroundImage') ?? '',
      siteShareSwitch: _safeGet<int>('siteShareSwitch') ?? 0,
      siteSearchSwitch: _safeGet<String>('siteSearchSwitch') ?? '0',
      siteSignInSwitch: _safeGet<String>('siteSignInSwitch') ?? '0',
      siteSpaceCollectionPageSwitch: _safeGet<int>('siteSpaceCollectionPageSwitch') ?? 0,
      siteAuthSwitch: _safeGet<String>('siteAuthSwitch') ?? '0',
      siteLoginType: _safeGet<String>('siteLoginType') ?? '0',
      sitePcMixThreadSwitch: _safeGet<String>('sitePcMixThreadSwitch') ?? '0',
      siteWechatLoginPid: _safeGet<String>('siteWechatLoginPid') ?? '',
      siteWechatLoginGid: _safeGet<String>('siteWechatLoginGid') ?? '',
      siteSignInUrl: _safeGet<String>('siteSignInUrl') ?? '',
      siteSystemMaintenance: _safeGet<String>('siteSystemMaintenance') ?? '0',
      siteActEntranceSwitch: _safeGet<String>('siteActEntranceSwitch') ?? '0',
      siteActEntranceUrl: _safeGet<String>('siteActEntranceUrl') ?? '',
      siteStat: _safeGet<String>('siteStat') ?? '',
      siteAuthor: _safeGet<String>('siteAuthor'),
      siteRecord: _safeGet<String>('siteRecord') ?? '',
      siteRecordCode: _safeGet<String>('siteRecordCode') ?? '',
      siteMasterScale: _safeGet<String>('siteMasterScale') ?? '',
      siteOpenSort: _safeGet<int>('siteOpenSort') ?? 0,
      openViewCount: _safeGet<String>('openViewCount') ?? '0',
      siteTitles: _safeGet<int>('siteTitles') ?? 0,
      siteRewards: (json['siteRewards'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(_safeMapCast(item)))
          .toList(),
      siteAreward: (json['siteAreward'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(_safeMapCast(item)))
          .toList(),
      siteRedpacket: (json['siteRedpacket'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(_safeMapCast(item)))
          .toList(),
      siteAnonymous: (json['siteAnonymous'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(_safeMapCast(item)))
          .toList(),
      sitePersonalletter: (json['sitePersonalletter'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(_safeMapCast(item)))
          .toList(),
      siteShop: (json['siteShop'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(_safeMapCast(item)))
          .toList(),
      siteUsergroup: (json['siteUsergroup'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(_safeMapCast(item)))
          .toList(),
      siteWithdrawal: (json['siteWithdrawal'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(_safeMapCast(item)))
          .toList(),
      siteComment: (json['siteComment'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(_safeMapCast(item)))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'siteManage': siteManage,
      'apiFreq': apiFreq,
      'siteUrl': siteUrl,
      'siteInstall': siteInstall,
      'siteCover': siteCover,
      'siteMinimumAmount': siteMinimumAmount,
      'loginaes': loginaes,
      'aesKey': aesKey,
      'aesIv': aesIv,
      'usernameLoginIsdisplay': usernameLoginIsdisplay,
      'openApiLog': openApiLog,
      'threadTab': threadTab,
      'version': version,
      'siteName': siteName,
      'siteTitle': siteTitle,
      'siteFabu': siteFabu,
      'siteKeywords': siteKeywords,
      'siteIntroduction': siteIntroduction,
      'siteMode': siteMode,
      'openExtFields': openExtFields,
      'siteClose': siteClose,
      'siteCloseMsg': siteCloseMsg,
      'siteFavicon': siteFavicon,
      'siteLogo': siteLogo,
      'siteHeaderLogo': siteHeaderLogo,
      'siteBackgroundImage': siteBackgroundImage,
      'siteUserBackgroundImage': siteUserBackgroundImage,
      'siteShareSwitch': siteShareSwitch,
      'siteSearchSwitch': siteSearchSwitch,
      'siteSignInSwitch': siteSignInSwitch,
      'siteSpaceCollectionPageSwitch': siteSpaceCollectionPageSwitch,
      'siteAuthSwitch': siteAuthSwitch,
      'siteLoginType': siteLoginType,
      'sitePcMixThreadSwitch': sitePcMixThreadSwitch,
      'siteWechatLoginPid': siteWechatLoginPid,
      'siteWechatLoginGid': siteWechatLoginGid,
      'siteSignInUrl': siteSignInUrl,
      'siteSystemMaintenance': siteSystemMaintenance,
      'siteActEntranceSwitch': siteActEntranceSwitch,
      'siteActEntranceUrl': siteActEntranceUrl,
      'siteStat': siteStat,
      'siteAuthor': siteAuthor,
      'siteRecord': siteRecord,
      'siteRecordCode': siteRecordCode,
      'siteMasterScale': siteMasterScale,
      'siteOpenSort': siteOpenSort,
      'openViewCount': openViewCount,
      'siteTitles': siteTitles,
      'siteRewards': siteRewards.map((item) => item.toJson()).toList(),
      'siteAreward': siteAreward.map((item) => item.toJson()).toList(),
      'siteRedpacket': siteRedpacket.map((item) => item.toJson()).toList(),
      'siteAnonymous': siteAnonymous.map((item) => item.toJson()).toList(),
      'sitePersonalletter': sitePersonalletter.map((item) => item.toJson()).toList(),
      'siteShop': siteShop.map((item) => item.toJson()).toList(),
      'siteUsergroup': siteUsergroup.map((item) => item.toJson()).toList(),
      'siteWithdrawal': siteWithdrawal.map((item) => item.toJson()).toList(),
      'siteComment': siteComment.map((item) => item.toJson()).toList(),
    };
  }
}