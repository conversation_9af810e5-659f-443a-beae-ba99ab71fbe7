/// 关注用户响应模型
class FollowResponse {
  /// 关注状态 (1: 已关注, 0: 未关注, 2: 互相关注)
  final int? followStatus;
  
  /// 关注ID
  final int? followId;
  
  /// 用户ID
  final int? userId;
  
  /// 被关注用户ID
  final String? toUserId;
  
  /// 创建时间
  final String? createdAt;
  
  /// 更新时间
  final String? updatedAt;

  FollowResponse({
    this.followStatus,
    this.followId,
    this.userId,
    this.toUserId,
    this.createdAt,
    this.updatedAt,
  });

  factory FollowResponse.fromJson(Map<String, dynamic> json) {
    return FollowResponse(
      followStatus: json['followStatus'] as int?,
      followId: json['followId'] as int?,
      userId: json['userId'] as int?,
      toUserId: json['toUserId'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'followStatus': followStatus,
      'followId': followId,
      'userId': userId,
      'toUserId': toUserId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  FollowResponse copyWith({
    int? followStatus,
    int? followId,
    int? userId,
    String? toUserId,
    String? createdAt,
    String? updatedAt,
  }) {
    return FollowResponse(
      followStatus: followStatus ?? this.followStatus,
      followId: followId ?? this.followId,
      userId: userId ?? this.userId,
      toUserId: toUserId ?? this.toUserId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'FollowResponse(followStatus: $followStatus, followId: $followId, userId: $userId, toUserId: $toUserId, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FollowResponse &&
        other.followStatus == followStatus &&
        other.followId == followId &&
        other.userId == userId &&
        other.toUserId == toUserId &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return followStatus.hashCode ^
        followId.hashCode ^
        userId.hashCode ^
        toUserId.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}