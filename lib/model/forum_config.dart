import 'set_site.dart';
import 'set_reg.dart';
import 'passport.dart';
import 'set_attach.dart';
import 'qcloud.dart';
import 'set_cash.dart';
import 'other.dart';
import 'lbs.dart';
import 'ucenter.dart';
import 'set_chatgpt.dart';
import 'forum_user.dart';
import 'agreement.dart';
import 'tgid_info.dart';
import 'emoji.dart';
import 'act_module.dart';

/// 论坛配置数据模型
class ForumConfig {
  final SetSite setSite;
  final SetReg setReg;
  final Passport passport;
  final SetAttach setAttach;
  final QCloud qcloud;
  final SetCash setCash;
  final Other other;
  final Lbs lbs;
  final UCenter ucenter;
  final SetChatgpt setChatgpt;
  final User user;
  final Agreement agreement;
  final List<TgidInfo> tgidInfo;
  final String? publishTime;
  final List<Emoji> emojis;
  final List<ActModule> actModule;
  final List<dynamic> popups;

  ForumConfig({
    required this.setSite,
    required this.setReg,
    required this.passport,
    required this.setAttach,
    required this.qcloud,
    required this.setCash,
    required this.other,
    required this.lbs,
    required this.ucenter,
    required this.setChatgpt,
    required this.user,
    required this.agreement,
    required this.tgidInfo,
    this.publishTime,
    required this.emojis,
    required this.actModule,
    required this.popups,
  });

  factory ForumConfig.fromJson(Map<String, dynamic> json) {
    // 辅助函数：安全地将 dynamic Map 转换为 Map<String, dynamic>
    Map<String, dynamic> _safeMapCast(dynamic value) {
      if (value == null) return <String, dynamic>{};
      if (value is Map<String, dynamic>) return value;
      if (value is Map) {
        return Map<String, dynamic>.from(value);
      }
      return <String, dynamic>{};
    }

    return ForumConfig(
      setSite: SetSite.fromJson(_safeMapCast(json['setSite'])),
      setReg: SetReg.fromJson(_safeMapCast(json['setReg'])),
      passport: Passport.fromJson(_safeMapCast(json['passport'])),
      setAttach: SetAttach.fromJson(_safeMapCast(json['setAttach'])),
      qcloud: QCloud.fromJson(_safeMapCast(json['qcloud'])),
      setCash: SetCash.fromJson(_safeMapCast(json['setCash'])),
      other: Other.fromJson(_safeMapCast(json['other'])),
      lbs: Lbs.fromJson(_safeMapCast(json['lbs'])),
      ucenter: UCenter.fromJson(_safeMapCast(json['ucenter'])),
      setChatgpt: SetChatgpt.fromJson(_safeMapCast(json['setChatgpt'])),
      user: User.fromJson(_safeMapCast(json['user'])),
      agreement: Agreement.fromJson(_safeMapCast(json['agreement'])),
      tgidInfo: (json['tgidInfo'] as List<dynamic>? ?? [])
          .map((item) => TgidInfo.fromJson(_safeMapCast(item)))
          .toList(),
      publishTime: json['publishTime']?.toString(),
      emojis: (json['emojis'] as List<dynamic>? ?? [])
          .map((item) => Emoji.fromJson(_safeMapCast(item)))
          .toList(),
      actModule: (json['actModule'] as List<dynamic>? ?? [])
          .map((item) => ActModule.fromJson(_safeMapCast(item)))
          .toList(),
      popups: json['popups'] ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'setSite': setSite.toJson(),
      'setReg': setReg.toJson(),
      'passport': passport.toJson(),
      'setAttach': setAttach.toJson(),
      'qcloud': qcloud.toJson(),
      'setCash': setCash.toJson(),
      'other': other.toJson(),
      'lbs': lbs.toJson(),
      'ucenter': ucenter.toJson(),
      'setChatgpt': setChatgpt.toJson(),
      'user': user.toJson(),
      'agreement': agreement.toJson(),
      'tgidInfo': tgidInfo.map((item) => item.toJson()).toList(),
      'publishTime': publishTime,
      'emojis': emojis.map((item) => item.toJson()).toList(),
      'actModule': actModule.map((item) => item.toJson()).toList(),
      'popups': popups,
    };
  }
}