/// 游戏角色信息数据模型
class GameRole {
  /// 角色ID
  final String drid;
  
  /// 服务器ID
  final String dsid;
  
  /// 角色名称
  final String drname;
  
  /// 服务器名称
  final String dsname;
  
  /// 角色等级
  final String drlevel;
  
  /// 角色游戏ID
  final int roleGid;
  
  /// 角色项目ID
  final int rolePid;
  
  /// 最后登录时间
  final String lastLogin;

  const GameRole({
    required this.drid,
    required this.dsid,
    required this.drname,
    required this.dsname,
    required this.drlevel,
    required this.roleGid,
    required this.rolePid,
    required this.lastLogin,
  });

  GameRole copyWith({
    String? drid,
    String? dsid,
    String? drname,
    String? dsname,
    String? drlevel,
    int? roleGid,
    int? rolePid,
    String? lastLogin,
  }) {
    return GameRole(
      drid: drid ?? this.drid,
      dsid: dsid ?? this.dsid,
      drname: drname ?? this.drname,
      dsname: dsname ?? this.dsname,
      drlevel: drlevel ?? this.drlevel,
      roleGid: roleGid ?? this.roleGid,
      rolePid: rolePid ?? this.rolePid,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  factory GameRole.fromJson(Map<String, dynamic> json) {
    return GameRole(
      drid: json['drid']?.toString() ?? '',
      dsid: json['dsid']?.toString() ?? '',
      drname: json['drname']?.toString() ?? '',
      dsname: json['dsname']?.toString() ?? '',
      drlevel: json['drlevel']?.toString() ?? '',
      roleGid: json['role_gid'] as int? ?? 0,
      rolePid: json['role_pid'] as int? ?? 0,
      lastLogin: json['last_login']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'drid': drid,
      'dsid': dsid,
      'drname': drname,
      'dsname': dsname,
      'drlevel': drlevel,
      'role_gid': roleGid,
      'role_pid': rolePid,
      'last_login': lastLogin,
    };
  }
}

/// 游戏角色V2信息数据模型 (接口二使用)
class GameRoleV2 {
  /// 角色ID (数字类型)
  final int drid;
  
  /// 服务器ID
  final String dsid;
  
  /// 角色名称
  final String drname;
  
  /// 服务器名称
  final String dsname;
  
  /// 角色等级
  final String drlevel;
  
  /// 角色项目ID
  final int rolePid;
  
  /// 角色游戏ID
  final int roleGid;
  
  /// 用户ID
  final int uid;
  
  /// 角色收藏ID
  final int roleFavoriteId;
  
  /// 角色顶级游戏ID
  final int roleTgid;
  
  /// 是否已选择
  final bool isPicked;

  /// 是否当前账号
  final bool isCurrent;

  const GameRoleV2({
    required this.drid,
    required this.dsid,
    required this.drname,
    required this.dsname,
    required this.drlevel,
    required this.rolePid,
    required this.roleGid,
    required this.uid,
    required this.roleFavoriteId,
    required this.roleTgid,
    required this.isPicked,
    required this.isCurrent
  });

  GameRoleV2 copyWith({
    int? drid,
    String? dsid,
    String? drname,
    String? dsname,
    String? drlevel,
    int? rolePid,
    int? roleGid,
    int? uid,
    int? roleFavoriteId,
    int? roleTgid,
    bool? isPicked,
    bool? isCurrent
  }) {
    return GameRoleV2(
      drid: drid ?? this.drid,
      dsid: dsid ?? this.dsid,
      drname: drname ?? this.drname,
      dsname: dsname ?? this.dsname,
      drlevel: drlevel ?? this.drlevel,
      rolePid: rolePid ?? this.rolePid,
      roleGid: roleGid ?? this.roleGid,
      uid: uid ?? this.uid,
      roleFavoriteId: roleFavoriteId ?? this.roleFavoriteId,
      roleTgid: roleTgid ?? this.roleTgid,
      isPicked: isPicked ?? this.isPicked,
      isCurrent: isCurrent ?? this.isCurrent
    );
  }

  factory GameRoleV2.fromJson(Map<String, dynamic> json) {
    return GameRoleV2(
      drid: json['drid'] as int? ?? 0,
      dsid: json['dsid']?.toString() ?? '',
      drname: json['drname']?.toString() ?? '',
      dsname: json['dsname']?.toString() ?? '',
      drlevel: json['drlevel']?.toString() ?? '',
      rolePid: json['role_pid'] as int? ?? 0,
      roleGid: json['role_gid'] as int? ?? 0,
      uid: json['uid'] as int? ?? 0,
      roleFavoriteId: json['role_favorite_id'] as int? ?? 0,
      roleTgid: json['role_tgid'] as int? ?? 0,
      isPicked: json['is_picked'] as bool? ?? false,
      isCurrent: json['is_current'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'drid': drid,
      'dsid': dsid,
      'drname': drname,
      'dsname': dsname,
      'drlevel': drlevel,
      'role_pid': rolePid,
      'role_gid': roleGid,
      'uid': uid,
      'role_favorite_id': roleFavoriteId,
      'role_tgid': roleTgid,
      'is_picked': isPicked,
      'is_current': isCurrent
    };
  }
}

/// 游戏角色列表响应数据模型
class GameRoleListData {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 角色列表
  final List<GameRole> data;

  const GameRoleListData({
    required this.state,
    required this.msg,
    required this.data,
  });

  GameRoleListData copyWith({
    int? state,
    String? msg,
    List<GameRole>? data,
  }) {
    return GameRoleListData(
      state: state ?? this.state,
      msg: msg ?? this.msg,
      data: data ?? this.data,
    );
  }

  factory GameRoleListData.fromJson(Map<String, dynamic> json) {
    return GameRoleListData(
      state: json['state'] as int? ?? 0,
      msg: json['msg'] as String? ?? '',
      data: (json['data'] as List<dynamic>?)
          ?.map((item) {
            if (item is Map<String, dynamic>) {
              return GameRole.fromJson(item);
            } else if (item is Map) {
              return GameRole.fromJson(Map<String, dynamic>.from(item));
            } else {
              return const GameRole(
                drid: '',
                dsid: '',
                drname: '',
                dsname: '',
                drlevel: '',
                roleGid: 0,
                rolePid: 0,
                lastLogin: '',
              );
            }
          })
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }
}

/// 游戏角色V2列表响应数据模型 (接口二使用)
class GameRoleListV2Data {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 数据对象
  final GameRoleListV2DataContent? data;

  const GameRoleListV2Data({
    required this.state,
    required this.msg,
    this.data,
  });

  GameRoleListV2Data copyWith({
    int? state,
    String? msg,
    GameRoleListV2DataContent? data,
  }) {
    return GameRoleListV2Data(
      state: state ?? this.state,
      msg: msg ?? this.msg,
      data: data ?? this.data,
    );
  }

  factory GameRoleListV2Data.fromJson(Map<String, dynamic> json) {
    // 注意：json 参数是响应转换器提取出来的 data 字段内容
    // 服务器响应结构：{"state": 1, "msg": "success", "data": {...}}
    // 这里的 json 实际上是上述响应中的 data 部分：{"list": [...]}
    return GameRoleListV2Data(
      state: 1, // 成功状态由响应转换器保证
      msg: 'success', // 消息由响应转换器处理
      data: GameRoleListV2DataContent.fromJson(json), // 直接解析传入的data内容
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data?.toJson(),
    };
  }
}

/// 游戏角色V2列表数据内容
class GameRoleListV2DataContent {
  /// 角色列表
  final List<GameRoleV2> list;

  const GameRoleListV2DataContent({
    required this.list,
  });

  GameRoleListV2DataContent copyWith({
    List<GameRoleV2>? list,
  }) {
    return GameRoleListV2DataContent(
      list: list ?? this.list,
    );
  }

  factory GameRoleListV2DataContent.fromJson(Map<String, dynamic> json) {
    return GameRoleListV2DataContent(
      list: (json['list'] as List<dynamic>?)
          ?.map((item) {
            if (item is Map<String, dynamic>) {
              return GameRoleV2.fromJson(item);
            } else if (item is Map) {
              return GameRoleV2.fromJson(Map<String, dynamic>.from(item));
            } else {
              return const GameRoleV2(
                drid: 0,
                dsid: '',
                drname: '',
                dsname: '',
                drlevel: '',
                rolePid: 0,
                roleGid: 0,
                uid: 0,
                roleFavoriteId: 0,
                roleTgid: 0,
                isPicked: false,
                isCurrent: false
              );
            }
          })
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'list': list.map((item) => item.toJson()).toList(),
    };
  }
}

/// 角色选择响应数据模型 (接口三使用)
class RolePickResponseData {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 数据对象 (可能为空)
  final Map<String, dynamic>? data;

  const RolePickResponseData({
    required this.state,
    required this.msg,
    this.data,
  });

  RolePickResponseData copyWith({
    int? state,
    String? msg,
    Map<String, dynamic>? data,
  }) {
    return RolePickResponseData(
      state: state ?? this.state,
      msg: msg ?? this.msg,
      data: data ?? this.data,
    );
  }

  factory RolePickResponseData.fromJson(Map<String, dynamic> json) {
    return RolePickResponseData(
      state: json['state'] as int? ?? 0,
      msg: json['msg'] as String? ?? '',
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data,
    };
  }
}

/// 绑定角色响应数据模型
class BindRolesResponse {
  /// 响应状态码
  final int state;
  
  /// 响应消息
  final String msg;
  
  /// 数据对象
  final BindRolesData? data;

  const BindRolesResponse({
    required this.state,
    required this.msg,
    this.data,
  });

  BindRolesResponse copyWith({
    int? state,
    String? msg,
    BindRolesData? data,
  }) {
    return BindRolesResponse(
      state: state ?? this.state,
      msg: msg ?? this.msg,
      data: data ?? this.data,
    );
  }

  factory BindRolesResponse.fromJson(Map<String, dynamic> json) {
    return BindRolesResponse(
      state: 1,
      msg: '',
      data:  BindRolesData.fromJson(json),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data?.toJson(),
    };
  }
}

/// 绑定角色响应数据内容
class BindRolesData {
  /// 是否有礼包
  final bool hasGift;

  const BindRolesData({
    required this.hasGift,
  });

  BindRolesData copyWith({
    bool? hasGift,
  }) {
    return BindRolesData(
      hasGift: hasGift ?? this.hasGift,
    );
  }

  factory BindRolesData.fromJson(Map<String, dynamic> json) {
    return BindRolesData(
      hasGift: json['has_gift'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'has_gift': hasGift,
    };
  }
}