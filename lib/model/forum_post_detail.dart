/// 帖子详情数据模型
class ForumPostDetail {
  final int threadId;
  final int postId;
  final int userId;
  final int categoryId;
  final int parentCategoryId;
  final int topicId;
  final String categoryName;
  final String parentCategoryName;
  final String title;
  final String displayTitle;
  final int viewCount;
  final int isApproved;
  final bool isStick;
  final bool isDraft;
  final bool isSite;
  final bool isAnonymous;
  final bool isFavorite;
  final double price;
  final double attachmentPrice;
  final dynamic paid;
  final bool isLike;
  final bool isReward;
  final String createdAt;
  final String issueAt;
  final String updatedAt;
  final String diffTime;
  final ForumDetailUser user;
  final dynamic group;
  final ForumDetailLikeReward likeReward;
  final ForumDetailDisplayTag displayTag;
  final ForumDetailPosition position;
  final ForumDetailAbility ability;
  final ForumDetailContent content;
  final int freewords;
  final bool userStickStatus;
  final int favorCount;
  final String topics;
  final int reportStatus;
  final String startShowTime;
  final int isBottom;
  final String aiRank;
  final String aiType;
  final int pid;
  final int uid;
  final String auditedBy;
  final String auditedAt;
  final String updatedBy;
  final String createdBy;
  final String location;
  final int from;
  final int addAiContent;
  final int isRecommend;
  final dynamic voteId;
  final dynamic vote;
  final int isMixThread;
  final List<dynamic> orderInfo;
  final int postWordNumber;
  final int foldPostCount;

  ForumPostDetail({
    required this.threadId,
    required this.postId,
    required this.userId,
    required this.categoryId,
    required this.parentCategoryId,
    required this.topicId,
    required this.categoryName,
    required this.parentCategoryName,
    required this.title,
    required this.displayTitle,
    required this.viewCount,
    required this.isApproved,
    required this.isStick,
    required this.isDraft,
    required this.isSite,
    required this.isAnonymous,
    required this.isFavorite,
    required this.price,
    required this.attachmentPrice,
    this.paid,
    required this.isLike,
    required this.isReward,
    required this.createdAt,
    required this.issueAt,
    required this.updatedAt,
    required this.diffTime,
    required this.user,
    this.group,
    required this.likeReward,
    required this.displayTag,
    required this.position,
    required this.ability,
    required this.content,
    required this.freewords,
    required this.userStickStatus,
    required this.favorCount,
    required this.topics,
    required this.reportStatus,
    required this.startShowTime,
    required this.isBottom,
    required this.aiRank,
    required this.aiType,
    required this.pid,
    required this.uid,
    required this.auditedBy,
    required this.auditedAt,
    required this.updatedBy,
    required this.createdBy,
    required this.location,
    required this.from,
    required this.addAiContent,
    required this.isRecommend,
    this.voteId,
    this.vote,
    required this.isMixThread,
    required this.orderInfo,
    required this.postWordNumber,
    required this.foldPostCount,
  });

  factory ForumPostDetail.fromJson(Map<String, dynamic> json) {
    return ForumPostDetail(
      threadId: json['threadId'] ?? 0,
      postId: json['postId'] ?? 0,
      userId: json['userId'] ?? 0,
      categoryId: json['categoryId'] ?? 0,
      parentCategoryId: json['parentCategoryId'] ?? 0,
      topicId: json['topicId'] ?? 0,
      categoryName: json['categoryName'] ?? '',
      parentCategoryName: json['parentCategoryName'] ?? '',
      title: json['title'] ?? '',
      displayTitle: json['display_title'] ?? '',
      viewCount: json['viewCount'] ?? 0,
      isApproved: json['isApproved'] ?? 0,
      isStick: json['isStick'] ?? false,
      isDraft: json['isDraft'] ?? false,
      isSite: json['isSite'] ?? false,
      isAnonymous: json['isAnonymous'] ?? false,
      isFavorite: json['isFavorite'] ?? false,
      price: (json['price'] ?? 0).toDouble(),
      attachmentPrice: (json['attachmentPrice'] ?? 0).toDouble(),
      paid: json['paid'],
      isLike: json['isLike'] ?? false,
      isReward: json['isReward'] ?? false,
      createdAt: json['createdAt'] ?? '',
      issueAt: json['issueAt'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
      diffTime: json['diffTime'] ?? '',
      user: ForumDetailUser.fromJson(json['user'] ?? {}),
      group: json['group'],
      likeReward: ForumDetailLikeReward.fromJson(json['likeReward'] ?? {}),
      displayTag: ForumDetailDisplayTag.fromJson(json['displayTag'] ?? {}),
      position: ForumDetailPosition.fromJson(json['position'] ?? {}),
      ability: ForumDetailAbility.fromJson(json['ability'] ?? {}),
      content: ForumDetailContent.fromJson(json['content'] ?? {}),
      freewords: json['freewords'] ?? 0,
      userStickStatus: json['userStickStatus'] ?? false,
      favorCount: json['favorCount'] ?? 0,
      topics: json['topics'] ?? '',
      reportStatus: json['report_status'] ?? 0,
      startShowTime: json['start_show_time'] ?? '',
      isBottom: json['is_bottom'] ?? 0,
      aiRank: json['ai_rank'] ?? '',
      aiType: json['ai_type'] ?? '',
      pid: json['pid'] ?? 0,
      uid: json['uid'] ?? 0,
      auditedBy: json['audited_by'] ?? '',
      auditedAt: json['audited_at'] ?? '',
      updatedBy: json['updated_by'] ?? '',
      createdBy: json['created_by'] ?? '',
      location: json['location'] ?? '',
      from: json['from'] ?? 0,
      addAiContent: json['add_ai_content'] ?? 0,
      isRecommend: json['is_recommend'] ?? 0,
      voteId: json['vote_id'],
      vote: json['vote'],
      isMixThread: json['is_mix_thread'] ?? 0,
      orderInfo: json['orderInfo'] ?? [],
      postWordNumber: json['postWordNumber'] ?? 0,
      foldPostCount: json['foldPostCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'threadId': threadId,
      'postId': postId,
      'userId': userId,
      'categoryId': categoryId,
      'parentCategoryId': parentCategoryId,
      'topicId': topicId,
      'categoryName': categoryName,
      'parentCategoryName': parentCategoryName,
      'title': title,
      'display_title': displayTitle,
      'viewCount': viewCount,
      'isApproved': isApproved,
      'isStick': isStick,
      'isDraft': isDraft,
      'isSite': isSite,
      'isAnonymous': isAnonymous,
      'isFavorite': isFavorite,
      'price': price,
      'attachmentPrice': attachmentPrice,
      'paid': paid,
      'isLike': isLike,
      'isReward': isReward,
      'createdAt': createdAt,
      'issueAt': issueAt,
      'updatedAt': updatedAt,
      'diffTime': diffTime,
      'user': user.toJson(),
      'group': group,
      'likeReward': likeReward.toJson(),
      'displayTag': displayTag.toJson(),
      'position': position.toJson(),
      'ability': ability.toJson(),
      'content': content.toJson(),
      'freewords': freewords,
      'userStickStatus': userStickStatus,
      'favorCount': favorCount,
      'topics': topics,
      'report_status': reportStatus,
      'start_show_time': startShowTime,
      'is_bottom': isBottom,
      'ai_rank': aiRank,
      'ai_type': aiType,
      'pid': pid,
      'uid': uid,
      'audited_by': auditedBy,
      'audited_at': auditedAt,
      'updated_by': updatedBy,
      'created_by': createdBy,
      'location': location,
      'from': from,
      'add_ai_content': addAiContent,
      'is_recommend': isRecommend,
      'vote_id': voteId,
      'vote': vote,
      'is_mix_thread': isMixThread,
      'orderInfo': orderInfo,
      'postWordNumber': postWordNumber,
      'foldPostCount': foldPostCount,
    };
  }
}

/// 用户详情信息
class ForumDetailUser {
  final int userId;
  final String nickname;
  final String avatar;
  final String badge;
  final String label;
  final String color;
  final dynamic medal;
  final int threadCount;
  final int followCount;
  final int fansCount;
  final int likedCount;
  final int questionCount;
  final bool isRealName;
  final String joinedAt;
  final int follow;

  ForumDetailUser({
    required this.userId,
    required this.nickname,
    required this.avatar,
    required this.badge,
    required this.label,
    required this.color,
    this.medal,
    required this.threadCount,
    required this.followCount,
    required this.fansCount,
    required this.likedCount,
    required this.questionCount,
    required this.isRealName,
    required this.joinedAt,
    required this.follow,
  });

  factory ForumDetailUser.fromJson(Map<String, dynamic> json) {
    return ForumDetailUser(
      userId: json['userId'] ?? 0,
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'] ?? '',
      badge: json['badge'] ?? '',
      label: json['label'] ?? '',
      color: json['color'] ?? '',
      medal: json['medal'],
      threadCount: json['threadCount'] ?? 0,
      followCount: json['followCount'] ?? 0,
      fansCount: json['fansCount'] ?? 0,
      likedCount: json['likedCount'] ?? 0,
      questionCount: json['questionCount'] ?? 0,
      isRealName: json['isRealName'] ?? false,
      joinedAt: json['joinedAt'] ?? '',
      follow: json['follow'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'nickname': nickname,
      'avatar': avatar,
      'badge': badge,
      'label': label,
      'color': color,
      'medal': medal,
      'threadCount': threadCount,
      'followCount': followCount,
      'fansCount': fansCount,
      'likedCount': likedCount,
      'questionCount': questionCount,
      'isRealName': isRealName,
      'joinedAt': joinedAt,
      'follow': follow,
    };
  }
}

/// 点赞奖励详情信息
class ForumDetailLikeReward {
  final List<dynamic> users;
  final int likePayCount;
  final int shareCount;
  final int postCount;

  ForumDetailLikeReward({
    required this.users,
    required this.likePayCount,
    required this.shareCount,
    required this.postCount,
  });

  factory ForumDetailLikeReward.fromJson(Map<String, dynamic> json) {
    return ForumDetailLikeReward(
      users: json['users'] ?? [],
      likePayCount: json['likePayCount'] ?? 0,
      shareCount: json['shareCount'] ?? 0,
      postCount: json['postCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'users': users,
      'likePayCount': likePayCount,
      'shareCount': shareCount,
      'postCount': postCount,
    };
  }
}

/// 显示标签详情
class ForumDetailDisplayTag {
  final bool isPoster;
  final bool isEssence;
  final dynamic isRedPack;
  final dynamic isReward;
  final bool isVote;

  ForumDetailDisplayTag({
    required this.isPoster,
    required this.isEssence,
    this.isRedPack,
    this.isReward,
    required this.isVote,
  });

  factory ForumDetailDisplayTag.fromJson(Map<String, dynamic> json) {
    return ForumDetailDisplayTag(
      isPoster: json['isPoster'] ?? false,
      isEssence: json['isEssence'] ?? false,
      isRedPack: json['isRedPack'],
      isReward: json['isReward'],
      isVote: json['isVote'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isPoster': isPoster,
      'isEssence': isEssence,
      'isRedPack': isRedPack,
      'isReward': isReward,
      'isVote': isVote,
    };
  }
}

/// 位置详情信息
class ForumDetailPosition {
  final String longitude;
  final String latitude;
  final String address;
  final String location;

  ForumDetailPosition({
    required this.longitude,
    required this.latitude,
    required this.address,
    required this.location,
  });

  factory ForumDetailPosition.fromJson(Map<String, dynamic> json) {
    return ForumDetailPosition(
      longitude: json['longitude'] ?? '',
      latitude: json['latitude'] ?? '',
      address: json['address'] ?? '',
      location: json['location'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'longitude': longitude,
      'latitude': latitude,
      'address': address,
      'location': location,
    };
  }
}

/// 权限详情信息
class ForumDetailAbility {
  final bool canEdit;
  final bool canDelete;
  final bool canEssence;
  final bool canPoster;
  final bool canStick;
  final bool canReply;
  final bool canViewPost;
  final bool canFreeViewPost;
  final bool canViewVideo;
  final bool canViewAttachment;
  final bool canDownloadAttachment;

  ForumDetailAbility({
    required this.canEdit,
    required this.canDelete,
    required this.canEssence,
    required this.canPoster,
    required this.canStick,
    required this.canReply,
    required this.canViewPost,
    required this.canFreeViewPost,
    required this.canViewVideo,
    required this.canViewAttachment,
    required this.canDownloadAttachment,
  });

  factory ForumDetailAbility.fromJson(Map<String, dynamic> json) {
    return ForumDetailAbility(
      canEdit: json['canEdit'] ?? false,
      canDelete: json['canDelete'] ?? false,
      canEssence: json['canEssence'] ?? false,
      canPoster: json['canPoster'] ?? false,
      canStick: json['canStick'] ?? false,
      canReply: json['canReply'] ?? false,
      canViewPost: json['canViewPost'] ?? false,
      canFreeViewPost: json['canFreeViewPost'] ?? false,
      canViewVideo: json['canViewVideo'] ?? false,
      canViewAttachment: json['canViewAttachment'] ?? false,
      canDownloadAttachment: json['canDownloadAttachment'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'canEdit': canEdit,
      'canDelete': canDelete,
      'canEssence': canEssence,
      'canPoster': canPoster,
      'canStick': canStick,
      'canReply': canReply,
      'canViewPost': canViewPost,
      'canFreeViewPost': canFreeViewPost,
      'canViewVideo': canViewVideo,
      'canViewAttachment': canViewAttachment,
      'canDownloadAttachment': canDownloadAttachment,
    };
  }
}

/// 内容详情信息
class ForumDetailContent {
  final String text;
  final Map<String, dynamic> indexes;
  final ForumDetailPoster? poster;
  final List<ForumDetailImage> images;
  final List<dynamic> videos;
  final String pureText;

  ForumDetailContent({
    required this.text,
    required this.indexes,
    this.poster,
    required this.images,
    required this.videos,
    required this.pureText,
  });

  factory ForumDetailContent.fromJson(Map<String, dynamic> json) {
    return ForumDetailContent(
      text: json['text'] ?? '',
      indexes: Map<String, dynamic>.from(json['indexes'] ?? {}),
      poster: json['poster'] != null ? ForumDetailPoster.fromJson(json['poster']) : null,
      images: (json['images'] as List? ?? [])
          .map((item) => ForumDetailImage.fromJson(item))
          .toList(),
      videos: json['videos'] ?? [],
      pureText: json['pure_text'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'indexes': indexes,
      'poster': poster?.toJson(),
      'images': images.map((image) => image.toJson()).toList(),
      'videos': videos,
      'pure_text': pureText,
    };
  }
}

/// 帖子作者详情信息
class ForumDetailPoster {
  final int id;
  final int userId;
  final int threadId;
  final dynamic replyPostId;
  final dynamic replyUserId;
  final dynamic commentPostId;
  final dynamic commentUserId;
  final String content;
  final String ip;
  final int port;
  final int replyCount;
  final int likeCount;
  final String createdAt;
  final String updatedAt;
  final dynamic deletedAt;
  final dynamic deletedUserId;
  final bool isFirst;
  final bool isComment;
  final int isApproved;
  final int tgid;
  final String startShowTime;
  final int reportStatus;
  final dynamic location;
  final int uid;
  final String auditedBy;
  final dynamic auditedAt;
  final String updatedBy;
  final int pid;
  final String displayContent;
  final int isTop;
  final int isFold;
  final dynamic foldedAt;
  final int createdByAi;

  ForumDetailPoster({
    required this.id,
    required this.userId,
    required this.threadId,
    this.replyPostId,
    this.replyUserId,
    this.commentPostId,
    this.commentUserId,
    required this.content,
    required this.ip,
    required this.port,
    required this.replyCount,
    required this.likeCount,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    this.deletedUserId,
    required this.isFirst,
    required this.isComment,
    required this.isApproved,
    required this.tgid,
    required this.startShowTime,
    required this.reportStatus,
    this.location,
    required this.uid,
    required this.auditedBy,
    this.auditedAt,
    required this.updatedBy,
    required this.pid,
    required this.displayContent,
    required this.isTop,
    required this.isFold,
    this.foldedAt,
    required this.createdByAi,
  });

  factory ForumDetailPoster.fromJson(Map<String, dynamic> json) {
    return ForumDetailPoster(
      id: json['id'] ?? 0,
      userId: json['user_id'] ?? 0,
      threadId: json['thread_id'] ?? 0,
      replyPostId: json['reply_post_id'],
      replyUserId: json['reply_user_id'],
      commentPostId: json['comment_post_id'],
      commentUserId: json['comment_user_id'],
      content: json['content'] ?? '',
      ip: json['ip'] ?? '',
      port: json['port'] ?? 0,
      replyCount: json['reply_count'] ?? 0,
      likeCount: json['like_count'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      deletedAt: json['deleted_at'],
      deletedUserId: json['deleted_user_id'],
      isFirst: json['is_first'] ?? false,
      isComment: json['is_comment'] ?? false,
      isApproved: json['is_approved'] ?? 0,
      tgid: json['tgid'] ?? 0,
      startShowTime: json['start_show_time'] ?? '',
      reportStatus: json['report_status'] ?? 0,
      location: json['location'],
      uid: json['uid'] ?? 0,
      auditedBy: json['audited_by'] ?? '',
      auditedAt: json['audited_at'],
      updatedBy: json['updated_by'] ?? '',
      pid: json['pid'] ?? 0,
      displayContent: json['display_content'] ?? '',
      isTop: json['is_top'] ?? 0,
      isFold: json['is_fold'] ?? 0,
      foldedAt: json['folded_at'],
      createdByAi: json['created_by_ai'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'thread_id': threadId,
      'reply_post_id': replyPostId,
      'reply_user_id': replyUserId,
      'comment_post_id': commentPostId,
      'comment_user_id': commentUserId,
      'content': content,
      'ip': ip,
      'port': port,
      'reply_count': replyCount,
      'like_count': likeCount,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
      'deleted_user_id': deletedUserId,
      'is_first': isFirst,
      'is_comment': isComment,
      'is_approved': isApproved,
      'tgid': tgid,
      'start_show_time': startShowTime,
      'report_status': reportStatus,
      'location': location,
      'uid': uid,
      'audited_by': auditedBy,
      'audited_at': auditedAt,
      'updated_by': updatedBy,
      'pid': pid,
      'display_content': displayContent,
      'is_top': isTop,
      'is_fold': isFold,
      'folded_at': foldedAt,
      'created_by_ai': createdByAi,
    };
  }
}

/// 图片详情信息
class ForumDetailImage {
  final int fileHeight;
  final int fileWidth;
  final String url;

  ForumDetailImage({
    required this.fileHeight,
    required this.fileWidth,
    required this.url,
  });

  factory ForumDetailImage.fromJson(Map<String, dynamic> json) {
    return ForumDetailImage(
      fileHeight: json['fileHeight'] ?? 0,
      fileWidth: json['fileWidth'] ?? 0,
      url: json['url'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fileHeight': fileHeight,
      'fileWidth': fileWidth,
      'url': url,
    };
  }
}