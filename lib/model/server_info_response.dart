/// 服务器信息响应数据模型
/// 处理server-info-service API响应中data字段的内容
class ServerInfoResponse {
  /// API信息列表
  final List<ApiInfo> apiInfos;

  ServerInfoResponse({
    required this.apiInfos,
  });

  factory ServerInfoResponse.fromJson(Map<String, dynamic> json) {
    return ServerInfoResponse(
      apiInfos: (json['api_infos'] as List?)
          ?.map((item) => ApiInfo.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'api_infos': apiInfos.map((item) => item.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'ServerInfoResponse{apiInfos: $apiInfos}';
  }

  /// 根据API键值获取对应的API信息
  ApiInfo? getApiInfo(String apiKey) {
    try {
      return apiInfos.firstWhere((api) => api.apiKey == apiKey);
    } catch (e) {
      return null;
    }
  }

  /// 根据API键值获取对应的API URL
  String? getApiUrl(String apiKey) {
    final apiInfo = getApiInfo(apiKey);
    return apiInfo?.apiInfo;
  }

  /// 根据API键值获取对应的API类型
  int? getApiType(String apiKey) {
    final apiInfo = getApiInfo(apiKey);
    return apiInfo?.apiType;
  }
}

/// API信息模型
class ApiInfo {
  /// API键值标识
  final String apiKey;
  
  /// API URL地址
  final String apiInfo;
  
  /// API类型
  final int apiType;

  ApiInfo({
    required this.apiKey,
    required this.apiInfo,
    required this.apiType,
  });

  factory ApiInfo.fromJson(Map<String, dynamic> json) {
    return ApiInfo(
      apiKey: json['api_key'] ?? '',
      apiInfo: json['api_info'] ?? '',
      apiType: json['api_type'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'api_key': apiKey,
      'api_info': apiInfo,
      'api_type': apiType,
    };
  }

  @override
  String toString() {
    return 'ApiInfo{apiKey: $apiKey, apiInfo: $apiInfo, apiType: $apiType}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ApiInfo &&
        other.apiKey == apiKey &&
        other.apiInfo == apiInfo &&
        other.apiType == apiType;
  }

  @override
  int get hashCode {
    return apiKey.hashCode ^ apiInfo.hashCode ^ apiType.hashCode;
  }
}