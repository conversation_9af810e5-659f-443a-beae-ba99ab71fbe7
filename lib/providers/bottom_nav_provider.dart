
import 'package:flutter/material.dart';
import 'dart:math';

class BottomNavProvider extends ChangeNotifier {
  int _countTab = 0;
  int _countLastTab = 0;
  int _currentIndex = 0;
  int _previousIndex = 0;
  int _lastGameTabTapTime = 0; // 记录上次点击游戏Tab的时间
  int _lastCommunityTabTapTime = 0; // 记录上次点击圈子Tab的时间
  static const int _oneTapPeriod = 800; // 单击间隔时间（毫秒）
  static const int _doubleTapTimeout = 300; // 双击超时时间（毫秒）
  static const int _communityTabIndex = 0; // 圈子Tab的索引
  static const int _gameTabIndex = 1; // 游戏Tab的索引

  int get currentIndex => _currentIndex;
  int get previousIndex => _previousIndex;

  void updateIndex(int index) {
    _previousIndex = _currentIndex;
    _currentIndex = index;
    _countTab++;
    notifyListeners();
  }

  /// 判断是否切换到圈子页面
  bool isChangeToCommunityPage() {
    return _currentIndex == _communityTabIndex && _previousIndex != _communityTabIndex;
  }

  /// 判断是否切换到游戏页面
  bool isChangeToGamesPage() {
    return _currentIndex == _gameTabIndex && _previousIndex != _gameTabIndex;
  }

  /// 判断是否单击圈子页面（带防抖）
  bool isTapCommunityPage() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final isCommunityTab = ((_currentIndex == _communityTabIndex) && (_previousIndex == _communityTabIndex));

    if (!isRealClick()) {
      return false;
    }

    if (isCommunityTab && (now - _lastCommunityTabTapTime) > _oneTapPeriod) {
      _lastCommunityTabTapTime = now;
      return true;
    }
    _lastCommunityTabTapTime = now;
    return false;

  }

  /// 判断是否单击圈子页面（带防抖）
  bool isTapGamesPage() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final isGameTab = ((_currentIndex == _gameTabIndex) && (_previousIndex == _gameTabIndex));

    // if (!isRealClick()) return false;

    if (isGameTab && (now - _lastGameTabTapTime) > _oneTapPeriod) {
      _lastGameTabTapTime = now;
      return true;
    }
    _lastGameTabTapTime = now;
    return false;

  }

  bool isRealClick() {
    if (_countTab == _countLastTab) {
      return false;
    } else {
      _countLastTab = _countTab;
      return true;
    }
  }
}