import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import '../pages/login/unified_login_page.dart';
import '../push/sq_push_manager.dart';
import '../services/user_storage_service.dart';
import '../model/user.dart';
import '../model/user_detail.dart';
import '../services/login_api_service.dart';
import '../services/profile_api_service.dart';
import '../components/common_alert.dart';
import '../track/login_track.dart';
import '../track/exit_track.dart';
import '../track/track.dart';
import '../main.dart' show navigatorKey;
import '../events/event_bus_manager.dart';

/// 用户数据存储和更新通知
class UserProvider extends ChangeNotifier {
  final UserDataManager _dataManager = UserDataManager();

  UserProvider() {
    // 监听数据管理器的变化
    _dataManager.onDataChanged = (String changeType, {String? message}) {
      notifyListeners();
      // 发送用户数据变更事件
      // 登录成功也发是因为当登录态过期重新登录后也当成是一次切换账号
      if(changeType == 'user_switched' || changeType == 'login_success') {
        EventBusManager.instance.fire(UserDataChangedEvent());
      }

    };
  }

  // Getters - 委托给UserDataManager
  User? get currentUser => _dataManager.currentUser;
  UserDetail? get currentUserDetail => _dataManager.currentUserDetail;
  UserDetail? get currentUserDetailInfo => _dataManager.currentUserDetailInfo;
  List<User> get userList => _dataManager.userList;
  bool get isLoggedIn => _dataManager.isLoggedIn;
  bool get isInitialized => _dataManager.isInitialized;

  /// 获取当前用户ID
  String? get currentUserId => _dataManager.currentUserId;

  /// 获取当前用户显示名称
  String get currentUserDisplayName => _dataManager.currentUserDisplayName;

  /// 获取当前用户的登录票据
  String? get currentTicket => _dataManager.currentTicket;

  /// 获取当前用户的刷新票据
  String? get currentRefreshTicket => _dataManager.currentRefreshTicket;

  /// 检查是否需要刷新票据
  bool get needsTokenRefresh => _dataManager.needsTokenRefresh;

  // 业务方法 - 委托给UserDataManager
  Future<void> initialize() => _dataManager.initialize();
  
  Future<void> loginSuccess({User? user}) => _dataManager.loginSuccess(user: user);
  
  Future<void> logout({String logoutType = LogoutType.manualExit}) => 
      _dataManager.logout(logoutType: logoutType);
  
  Future<void> updateUserInfo(User user) => _dataManager.updateUserInfo(user);
  
  Future<(bool, String?)> switchUser(User user) => _dataManager.switchUser(user);
  
  Future<void> removeUser(String userId) => _dataManager.removeUser(userId);
  
  Future<void> clearAllUsers() => _dataManager.clearAllUsers();
  
  List<User> getRecentUsers({int limit = 5}) => _dataManager.getRecentUsers(limit: limit);
  
  bool hasUserInHistory(String userId) => _dataManager.hasUserInHistory(userId);
  
  User? getUserById(String userId) => _dataManager.getUserById(userId);
  
  Future<bool> refreshUserToken() => _dataManager.refreshUserToken();
  
  Map<String, int> getUserStats() => _dataManager.getUserStats();
  
  List<User> getUsersByLoginType(String loginType) => _dataManager.getUsersByLoginType(loginType);
  
  Future<bool> fetchCurrentUserDetail() => _dataManager.fetchCurrentUserDetail();
  
  Future<bool> fetchUserDetailInfo() => _dataManager.fetchUserDetailInfo();

  @override
  void dispose() {
    _dataManager.onDataChanged = null;
    super.dispose();
  }
}

/// 用户数据管理器
/// 作为唯一的用户数据管理中心，负责数据管理、业务逻辑和状态通知
/// 使用manager可以不传context
class UserDataManager {
  static final UserDataManager _instance = UserDataManager._internal();
  factory UserDataManager() => _instance;
  UserDataManager._internal();

  final UserStorageService _storageService = UserStorageService();

  User? _cachedUser;
  UserDetail? _cachedUserDetail;
  UserDetail? _cachedUserDetailInfo;
  bool _isLoggedIn = false;
  bool _isInitialized = false;
  List<User> _userList = [];

  /// 数据变更回调
  Function(String changeType, {String? message})? onDataChanged;

  /// 清除缓存的用户信息
  void clearCache() {
    _cachedUser = null;
    _cachedUserDetail = null;
    _cachedUserDetailInfo = null;
    _isLoggedIn = false;
    
    /// 触发数据变更回调
    onDataChanged?.call('cache_cleared', message: '用户缓存已清除');
  }

  // Getters - 提供外部访问接口
  User? get currentUser => _cachedUser;
  UserDetail? get currentUserDetail => _cachedUserDetail;
  UserDetail? get currentUserDetailInfo => _cachedUserDetailInfo;
  bool get isLoggedIn => _isLoggedIn;
  bool get isInitialized => _isInitialized;
  List<User> get userList => _userList;
  
  /// 获取当前用户ID
  String? get currentUserId => _cachedUser?.muid;
  
  /// 获取当前用户显示名称
  String get currentUname => _cachedUser?.loginInfo.muname ?? '';
  String get currentUserDisplayName => _cachedUser?.alias ?? '';
  
  /// 获取当前用户的登录票据
  String? get currentTicket => _cachedUser?.ticket;
  
  /// 获取当前用户的刷新票据
  String? get currentRefreshTicket => _cachedUser?.refreshTicket;
  
  /// 检查是否需要刷新票据
  bool get needsTokenRefresh {
    if (_cachedUser == null) return false;
    return false;
  }

  /// 初始化用户数据
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // 从本地存储加载用户数据
      _cachedUser = await _storageService.getCurrentUser();
      _userList = await _storageService.getUserList();
      _isLoggedIn = _cachedUser != null && _isValidSession(_cachedUser!);
      if (_isLoggedIn) {
        SqPushManager().tryPushToken(_cachedUser?.muid ?? "");
      }
      _isInitialized = true;
      onDataChanged?.call('initialized', message: '用户数据初始化完成');
    } catch (e) {
      debugPrint('用户数据初始化失败: $e');
      _isInitialized = true;
      onDataChanged?.call('init_failed', message: '用户数据初始化失败: $e');
    }
  }

  /// 登录成功处理
  Future<void> loginSuccess({User? user}) async {
    if (user != null) {
      _cachedUser = user;
      await _storageService.saveCurrentUser(user);
      await _updateUserList(user);
      SqPushManager().tryPushToken(_cachedUser?.muid ?? "");
      
      // 设置埋点SDK的用户ID
      if (user.muid != null) {
        SqTrackManager.setUserId(user.muid!);
      }
    }
    
    _isLoggedIn = true;
    onDataChanged?.call('login_success', message: '用户登录成功');
  }

  /// 退出登录
  /// [logoutType] 登出类型：LogoutType.switchAccount(切换账号) 或 LogoutType.manualExit(手动退出)
  Future<void> logout({String logoutType = LogoutType.manualExit}) async {
    try {
      // 获取当前用户的登录类型用于埋点
      int? currentLoginType;
      if (_cachedUser != null) {
        currentLoginType = _getLoginTypeCode(_cachedUser!.loginType);
      }
      
      // 保存当前用户到历史记录（如果有的话）
      if (_cachedUser != null) {
        await _updateUserList(_cachedUser!);
      }
      
      // 清除当前用户状态
      _cachedUser = null;
      _cachedUserDetail = null;
      _isLoggedIn = false;
      
      // 清除本地存储的当前用户数据
      await _storageService.saveCurrentUser(null);
      
      // 账号登出埋点
      if (currentLoginType != null) {
        ExitTrack.trackLogoutSucc(
          logoutType: logoutType,
          loginType: currentLoginType,
        );
      }
      
      onDataChanged?.call('logout_success', message: '用户退出登录成功');
    } catch (e) {
      debugPrint('退出登录失败: $e');
      // 即使出错也要清除状态，确保用户能够退出
      _cachedUser = null;
      _cachedUserDetail = null;
      _isLoggedIn = false;
      onDataChanged?.call('logout_error', message: '退出登录失败: $e');
    }
  }

  /// 验证用户会话是否有效
  bool _isValidSession(User user) {
    // 检查用户票据是否存在
    if (user.ticket.isEmpty) {
      return false;
    }

    // 这里可以添加更多的会话验证逻辑
    // 比如检查票据是否过期、是否需要刷新等

    return true;
  }

  /// 更新用户列表
  Future<void> _updateUserList(User user) async {
    // 移除已存在的相同用户
    _userList.removeWhere((u) => u.muid == user.muid);
    // 添加用户（存储服务会自动按 lastLoginAt 排序）
    _userList.add(user);
    
    // 保存到存储服务（会自动排序）
    await _storageService.saveUserList(_userList);
    // 重新加载以确保本地列表也是排序后的
    _userList = await _storageService.getUserList();
  }

  /// 将登录类型字符串转换为数字代码
  /// 用于埋点上报
  int _getLoginTypeCode(String loginType) {
    switch (loginType) {
      case 'password':
      case 'account':
        return ExitLoginType.accountPassword; // 1：账号密码
      case 'phone':
      case 'mobile':
        return ExitLoginType.phoneLogin; // 2：登录手机  
      case 'wechat':
      case 'weixin':
        return ExitLoginType.wechat; // 3：微信
      default:
        return ExitLoginType.accountPassword; // 默认为账号密码
    }
  }

  /// 更新用户信息
  Future<void> updateUserInfo(User user) async {
    _cachedUser = user;
    await _storageService.saveCurrentUser(user);
    await _updateUserList(user);
    onDataChanged?.call('user_updated', message: '用户信息更新成功');
  }

  /// 切换用户账号
  /// 返回 (success, errorMessage)
  Future<(bool, String?)> switchUser(User user) async {
    try {
      // 记录当前用户的登出（切换账号）
      if (_cachedUser != null) {
        final currentLoginType = _getLoginTypeCode(_cachedUser!.loginType);
        ExitTrack.trackLogoutSucc(
          logoutType: LogoutType.switchAccount,
          loginType: currentLoginType,
        );
      }
      
      LoginTrack.trackLoginInvoke(LoginTrack.getLoginType(user.loginType), TrackLoginWay.historyAccount);
      // 先调用刷新票据接口验证账号有效性
      final api = LoginApiService();
      final resp = await api.refreshTicket(
        appTicket: user.ticket,
        appRefreshTicket: user.refreshTicket,
      );

      if (resp.success && resp.data != null) {
        // 刷新成功，更新用户信息
        final newInfo = resp.data!;
        final updatedUser = user.copyWith(
          loginInfo: user.loginInfo.copyWith(
            ticket: newInfo.ticket,
            refreshTicket: newInfo.refreshTicket,
            lastLoginAt: DateTime.now(), // 切换用户刷新票据时更新登录时间
          ),
        );
        
        _cachedUser = updatedUser;
        _isLoggedIn = _isValidSession(updatedUser);
        
        await _storageService.saveCurrentUser(updatedUser);
        await _updateUserList(updatedUser);
        SqPushManager().tryPushToken(_cachedUser?.muid ?? "");
        
        // 设置埋点SDK的用户ID
        if (updatedUser.muid != null) {
          SqTrackManager.setUserId(updatedUser.muid!);
        }
        
        LoginTrack.trackLoginSuccess(LoginTrack.getLoginType(user.loginType), TrackLoginWay.historyAccount);
        onDataChanged?.call('user_switched', message: '用户切换成功');
        return (true, null);
      } else {
        await _showErrorDialog(resp.code, resp.message);
        LoginTrack.trackLoginFail(LoginTrack.getLoginType(user.loginType), TrackLoginWay.historyAccount, resp.message);
        return (false, resp.message);
      }
    } catch (e) {
      debugPrint('切换账号失败: $e');
      LoginTrack.trackLoginFail(LoginTrack.getLoginType(user.loginType), TrackLoginWay.historyAccount, '切换账号失败: $e');
      return (false, '切换账号失败: $e');
    }
  }

  /// 显示错误弹窗
  Future<void> _showErrorDialog(int errorCode, String message) async {
    final context = navigatorKey.currentContext;
    if (context == null) return;
    CommonAlert.show(
      context: context,
      title: '提示',
      content: '账号登录态已失效，请重新登录',
      confirmText: '去重新登录',
      barrierDismissible: false,
      cancelText: '我知道了',
      onConfirm: () {
        // 使用 SchedulerBinding 确保在下一帧执行跳转，避免与弹窗关闭冲突
        SchedulerBinding.instance.addPostFrameCallback((_) {
          final globalContext = navigatorKey.currentContext;
          if (globalContext != null && globalContext.mounted) {
            final route = MaterialPageRoute(
              builder: (context) => const UnifiedLoginPage(directPop: true),
            );
            Navigator.of(globalContext).push(route);
          }
        });
      },
    );
  }

  /// 删除历史账号
  Future<void> removeUser(String userId) async {
    _userList.removeWhere((user) => user.muid == userId);
    // 保存更新后的列表（会自动排序）
    await _storageService.saveUserList(_userList);
    
    // 如果删除的是当前用户，需要清除当前用户
    if (_cachedUser?.muid == userId) {
      await logout(logoutType: LogoutType.switchAccount);
    }
    
    onDataChanged?.call('user_removed', message: '用户已删除');
  }

  /// 清除所有用户数据
  Future<void> clearAllUsers() async {
    await _storageService.clearAllUsers();
    _userList.clear();
    _cachedUser = null;
    _isLoggedIn = false;
    onDataChanged?.call('all_users_cleared', message: '所有用户数据已清除');
  }

  /// 获取用户历史登录记录（按最后登录时间排序，最新的在前）
  List<User> getRecentUsers({int limit = 5}) {
    return _userList.take(limit).toList();
  }

  /// 检查用户是否在历史记录中
  bool hasUserInHistory(String userId) {
    return _userList.any((user) => user.muid == userId);
  }

  /// 根据用户ID获取用户信息
  User? getUserById(String userId) {
    try {
      return _userList.firstWhere((user) => user.muid == userId);
    } catch (e) {
      return null;
    }
  }

  /// 刷新用户票据
  Future<bool> refreshUserToken() async {
    if (_cachedUser == null) return false;
    
    try {
      LoginTrack.trackLoginInvoke(LoginTrack.getLoginType(_cachedUser!.loginType), TrackLoginWay.autoLogin);
      final api = LoginApiService();
      final resp = await api.refreshTicket(
        appTicket: _cachedUser!.ticket,
        appRefreshTicket: _cachedUser!.refreshTicket,
      );

      if (resp.success && resp.data != null) {
        final newInfo = resp.data!;
        final updatedUser = _cachedUser!.copyWith(
          loginInfo: _cachedUser!.loginInfo.copyWith(
            ticket: newInfo.ticket,
            refreshTicket: newInfo.refreshTicket,
            lastLoginAt: DateTime.now(), // 刷新票据时更新登录时间
          ),
        );
        _cachedUser = updatedUser;
        await _storageService.saveCurrentUser(updatedUser);
        await _updateUserList(updatedUser);
        
        // 设置埋点SDK的用户ID
        if (updatedUser.muid != null) {
          SqTrackManager.setUserId(updatedUser.muid!);
        }
        
        onDataChanged?.call('token_refreshed', message: '用户票据刷新成功');
        LoginTrack.trackLoginSuccess(LoginTrack.getLoginType(_cachedUser!.loginType), TrackLoginWay.autoLogin);
        return true;
      }

      LoginTrack.trackLoginFail(LoginTrack.getLoginType(_cachedUser!.loginType), TrackLoginWay.autoLogin, '刷新票据失败: ${resp.message}');
      return false;
    } catch (e) {
      debugPrint('刷新用户票据失败: $e');
      LoginTrack.trackLoginFail(LoginTrack.getLoginType(_cachedUser!.loginType), TrackLoginWay.autoLogin, '刷新用户票据失败: $e');
      return false;
    }
  }

  /// 获取用户统计信息
  Map<String, int> getUserStats() {
    final stats = <String, int>{};
    
    for (final user in _userList) {
      final loginType = user.loginType;
      stats[loginType] = (stats[loginType] ?? 0) + 1;
    }
    
    return stats;
  }

  /// 按登录类型获取用户列表
  List<User> getUsersByLoginType(String loginType) {
    return _userList.where((user) => user.loginType == loginType).toList();
  }

  /// 获取当前用户基本信息
  /// 如果当前用户已登录且有票据，会调用API获取用户的详细信息
  Future<bool> fetchCurrentUserDetail() async {
    if (!_isLoggedIn || _cachedUser == null || _cachedUser!.ticket.isEmpty) {
      debugPrint('用户未登录或票据为空，无法获取详细信息');
      return false;
    }

    try {
      final api = LoginApiService();
      final response = await api.getUserBasicInfo(_cachedUser!.ticket);

      if (response.success && response.data != null) {
        _cachedUserDetail = response.data!;

        // 更新当前用户信息
        final updatedUser = _cachedUser!.copyWith(userDetail: _cachedUserDetail!,);
        _cachedUser = updatedUser;
        // 缓存更新后的用户信息
        await _storageService.saveCurrentUser(updatedUser);
        await _updateUserList(updatedUser);

        debugPrint('获取用户详细信息成功: ${_cachedUserDetail?.alias}');
        onDataChanged?.call('user_detail_fetched', message: '用户详细信息获取成功');
        return true;
      } else {
        debugPrint('获取用户详细信息失败: ${response.message}');
        _cachedUserDetail = null;
        return false;
      }
    } catch (e) {
      debugPrint('获取用户详细信息异常: $e');
      _cachedUserDetail = null;
      return false;
    } finally {
      onDataChanged?.call('user_detail_fetch_completed', message: '用户详细信息获取完成');
    }
  }

  Future<bool> fetchUserDetailInfo() async {
    if (!_isLoggedIn || _cachedUser == null || _cachedUser!.ticket.isEmpty) {
      debugPrint('用户未登录或票据为空，无法获取详细信息');
      return false;
    }

    try {
      final api = ProfileApiService();
      final response = await api.getUserDetailInfo(_cachedUser!.ticket);

      if (response.success && response.data != null) {
        _cachedUserDetailInfo = response.data!;

        // 更新当前用户信息
        final updatedUser = _cachedUser!.copyWith(userDetail: _cachedUserDetailInfo!);
        _cachedUser = updatedUser;
        // 缓存更新后的用户信息
        await _storageService.saveCurrentUser(updatedUser);
        await _updateUserList(updatedUser);

        onDataChanged?.call('user_detail_info_fetched', message: '用户详细信息获取成功');
        return true;
      } else {
        debugPrint('获取用户详细信息失败: ${response.message}');
        _cachedUserDetailInfo = null;
        return false;
      }
    } catch (e) {
      debugPrint('获取用户详细信息异常: $e');
      _cachedUserDetailInfo = null;
      return false;
    } finally {
      onDataChanged?.call('user_detail_info_fetch_completed', message: '用户详细信息获取完成');
    }
  }
}