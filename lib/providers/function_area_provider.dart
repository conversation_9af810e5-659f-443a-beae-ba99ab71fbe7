import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../model/function_area.dart';
import '../utils/log_util.dart';
import '../net/api/game_circle_service.dart';

/// 功能区数据管理
/// 负责管理功能区列表的状态和网络请求
class FunctionAreaProvider extends ChangeNotifier {
  List<FunctionArea> _functionAreas = [];
  bool _isLoading = false;
  String? _error;
  
  // 缓存当前加载的参数，用于判断是否需要重新请求
  String? _currentTgid;
  String? _currentCircleId;

  // Getters
  List<FunctionArea> get functionAreas => _functionAreas;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasData => _functionAreas.isNotEmpty;
  String? get currentTgid => _currentTgid;
  String? get currentCircleId => _currentCircleId;

  /// 加载功能区列表（主要方法）
  /// [tgid] 游戏圈的tgid
  /// [circleId] 游戏圈的circleId
  /// [context] 上下文
  /// [forceRefresh] 是否强制刷新，默认false会使用缓存
  Future<bool> loadFunctionAreas({
    required String tgid,
    required String circleId,
    required BuildContext context,
    bool forceRefresh = false,
  }) async {
    try {
      // 检查是否需要重新请求
      if (!forceRefresh && 
          _currentTgid == tgid && 
          _currentCircleId == circleId && 
          _functionAreas.isNotEmpty) {
        LogUtil.d(
          '功能区数据已存在，跳过请求: tgid=$tgid, circleId=$circleId',
          tag: 'FunctionAreaProvider',
        );
        return true;
      }

      LogUtil.d(
        '开始加载功能区列表: tgid=$tgid, circleId=$circleId, forceRefresh=$forceRefresh',
        tag: 'FunctionAreaProvider',
      );
      
      setLoading(true);
      GameCircleService.context = context;

      // 1. 先加载功能区列表
      final response = await GameCircleService.getFunctionAreaList(
        context: context,
        tgid: tgid,
        circleId: circleId,
      );

      LogUtil.d(
        '功能区列表请求完成: success=${response.success}, data=${response.data}',
        tag: 'FunctionAreaProvider',
      );

      if (response.data != null) {
        final data = response.data!;
        LogUtil.d(
          '功能区列表加载成功，获取到 ${data.length} 个功能区',
          tag: 'FunctionAreaProvider',
        );

        // 2. 加载功能区状态（角标信息）
        await _loadFunctionAreaStatus(tgid, circleId, data, context);

        // 3. 更新数据和缓存参数
        _functionAreas = data;
        _currentTgid = tgid;
        _currentCircleId = circleId;
        _error = null;
        _isLoading = false;

        LogUtil.d('功能区数据更新成功: ${_functionAreas.length} 个功能区',
                  tag: 'FunctionAreaProvider');
        notifyListeners();
        return true;
      } else {
        LogUtil.w(
          '功能区列表加载失败: success=${response.success}, msg=${response.message}',
          tag: 'FunctionAreaProvider',
        );
        setError(response.message ?? '加载功能区列表失败');
        return false;
      }
    } catch (e) {
      LogUtil.e('加载功能区列表异常: $e', tag: 'FunctionAreaProvider');
      setError('加载功能区列表异常: $e');
      return false;
    }
  }

  /// 加载功能区状态（角标信息）
  Future<void> _loadFunctionAreaStatus(
    String tgid,
    String circleId,
    List<FunctionArea> functionAreas,
    BuildContext context,
  ) async {
    try {
      LogUtil.d('开始加载功能区状态...', tag: 'FunctionAreaProvider');

      final statusResponse = await GameCircleService.getFunctionAreaStatus(
        context: context,
        tgid: tgid,
        circleId: circleId,
      );

      if (statusResponse.data != null) {
        final statusData = statusResponse.data!;
        LogUtil.d(
          '功能区状态加载成功，获取到 ${statusData.length} 个状态',
          tag: 'FunctionAreaProvider',
        );

        // 将状态信息映射到功能区列表
        for (final status in statusData) {
          try {
            final functionArea = functionAreas.firstWhere(
              (area) => area.areaModule == status.areaModule,
            );
            functionArea.badgeUrl = status.badgeUrl;
          } catch (e) {
            LogUtil.w('未找到对应的功能区: ${status.areaModule}', tag: 'FunctionAreaProvider');
          }
        }

        LogUtil.d('功能区状态映射完成', tag: 'FunctionAreaProvider');
      } else {
        LogUtil.w('功能区状态加载失败: ${statusResponse.message}', tag: 'FunctionAreaProvider');
      }
    } catch (e) {
      LogUtil.e('加载功能区状态异常: $e', tag: 'FunctionAreaProvider');
    }
  }

  /// 强制刷新功能区数据
  Future<bool> refreshFunctionAreas(BuildContext context) async {
    if (_currentTgid != null && _currentCircleId != null) {
      return await loadFunctionAreas(
        tgid: _currentTgid!,
        circleId: _currentCircleId!,
        context: context,
        forceRefresh: true,
      );
    }
    return false;
  }

  /// 检查是否有当前圈子的数据
  bool hasDataForCircle(String tgid, String circleId) {
    return _currentTgid == tgid && 
           _currentCircleId == circleId && 
           _functionAreas.isNotEmpty;
  }

  /// 更新功能区数据（兼容旧版本）
  void updateFunctionAreas(List<FunctionArea> functionAreas) {
    _functionAreas = functionAreas;
    _error = null;
    _isLoading = false;

    LogUtil.d('功能区数据更新成功: ${_functionAreas.length} 个功能区',
              tag: 'FunctionAreaProvider');
    notifyListeners();
  }

  /// 清除数据
  void clearData() {
    _functionAreas = [];
    _error = null;
    _isLoading = false;
    _currentTgid = null;
    _currentCircleId = null;

    LogUtil.d('功能区数据已清除', tag: 'FunctionAreaProvider');
    notifyListeners();
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      if (loading) {
        _error = null;
      }
      notifyListeners();
    }
  }

  /// 设置错误状态
  void setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  /// 根据模块名查找功能区
  FunctionArea? findByModule(String areaModule) {
    try {
      return _functionAreas.firstWhere(
        (area) => area.areaModule == areaModule,
      );
    } catch (e) {
      return null;
    }
  }

  /// 根据areaModule查找功能区
  FunctionArea? findByName(String areaModule) {
    try {
      return _functionAreas.firstWhere(
        (area) => area.areaModule == areaModule,
      );
    } catch (e) {
      return null;
    }
  }
  /// 根据areaName查找功能区
  FunctionArea? findByAreaName(String areaName) {
    try {
      return _functionAreas.firstWhere(
        (area) => area.areaName == areaName,
      );
    } catch (e) {
      return null;
    }
  }

  /// 获取所有功能区数据的只读副本
  List<FunctionArea> getAllFunctionAreas() {
    return List.unmodifiable(_functionAreas);
  }

  /// 静态方法：从上下文获取FunctionAreaProvider实例
  static FunctionAreaProvider? of(BuildContext context, {bool listen = false}) {
    try {
      return Provider.of<FunctionAreaProvider>(context, listen: listen);
    } catch (e) {
      return null;
    }
  }
}

enum FunctionModuleType {
  daijinquan, //daijinquan
  speed_mall, //tehuishangcheng
  gift_center, //libaozhongxin
  coin_mall, //jifenshangcheng
}
