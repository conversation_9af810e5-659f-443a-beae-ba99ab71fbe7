import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/game_circle.dart';
import '../track/track.dart';

/// 游戏圈选择状态管理
/// 负责管理用户是否已选择游戏圈的状态
class GameCircleProvider extends ChangeNotifier {
  bool _hasSelectedGameCircle = false;
  String? _selectedGameCircleId;
  String? _selectedGameCircleName;
  GameCircle? _selectedGameCircle;
  bool _isLoaded = false;

  // 常量
  static const String _keyHasSelected = 'has_selected_game_circle';
  static const String _keySelectedId = 'selected_game_circle_id';
  static const String _keySelectedName = 'selected_game_circle_name';
  static const String _keySelectedGameCircle = 'selected_game_circle_data';

  // Getters
  bool get hasSelectedGameCircle => _hasSelectedGameCircle;
  String? get selectedGameCircleId => _selectedGameCircleId;
  String? get selectedGameCircleName => _selectedGameCircleName;
  GameCircle? get selectedGameCircle => _selectedGameCircle;
  bool get isLoaded => _isLoaded;

  /// 初始化，从本地存储加载状态
  Future<void> initialize() async {
    if (_isLoaded) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      _hasSelectedGameCircle = prefs.getBool(_keyHasSelected) ?? false;
      _selectedGameCircleId = prefs.getString(_keySelectedId);
      _selectedGameCircleName = prefs.getString(_keySelectedName);
      
      // 加载完整的GameCircle数据
      final gameCircleData = prefs.getString(_keySelectedGameCircle);
      if (gameCircleData != null) {
        try {
          final Map<String, dynamic> json = jsonDecode(gameCircleData);
          _selectedGameCircle = GameCircle.fromJson(json);
          
          // 初始化时更新track的tgid缓存
          if (_selectedGameCircle?.tgid != null) {
            SqTrackManager.updateTgid(_selectedGameCircle!.tgid);
          }
        } catch (e) {
          debugPrint('解析GameCircle数据失败: $e');
          _selectedGameCircle = null;
        }
      }
      
      _isLoaded = true;
      
      debugPrint('GameCircle状态加载: hasSelected=$_hasSelectedGameCircle, id=$_selectedGameCircleId, name=$_selectedGameCircleName');
      notifyListeners();
    } catch (e) {
      debugPrint('GameCircle状态加载失败: $e');
      _isLoaded = true;
      notifyListeners();
    }
  }

  /// 设置已选择游戏圈
  Future<void> selectGameCircle(String gameCircleId, String gameCircleName, [GameCircle? gameCircle]) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyHasSelected, true);
      await prefs.setString(_keySelectedId, gameCircleId);
      await prefs.setString(_keySelectedName, gameCircleName);
      
      // 保存完整的GameCircle数据
      if (gameCircle != null) {
        final gameCircleJson = jsonEncode(gameCircle.toJson());
        await prefs.setString(_keySelectedGameCircle, gameCircleJson);
        _selectedGameCircle = gameCircle;
      }
      
      _hasSelectedGameCircle = true;
      _selectedGameCircleId = gameCircleId;
      _selectedGameCircleName = gameCircleName;
      
      // 更新track的tgid缓存
      final tgid = gameCircle?.tgid;
      if (tgid != null) {
        SqTrackManager.updateTgid(tgid);
      }
      
      debugPrint('游戏圈选择已保存: id=$gameCircleId, name=$gameCircleName');
      notifyListeners();
    } catch (e) {
      debugPrint('保存游戏圈选择失败: $e');
    }
  }

  /// 清除选择状态（用于测试或重置）
  Future<void> clearSelection() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyHasSelected);
      await prefs.remove(_keySelectedId);
      await prefs.remove(_keySelectedName);
      await prefs.remove(_keySelectedGameCircle);
      
      _hasSelectedGameCircle = false;
      _selectedGameCircleId = null;
      _selectedGameCircleName = null;
      _selectedGameCircle = null;
      
      // 清除track的tgid缓存
      SqTrackManager.updateTgid(null);
      
      debugPrint('游戏圈选择已清除');
      notifyListeners();
    } catch (e) {
      debugPrint('清除游戏圈选择失败: $e');
    }
  }

  /// 获取选择状态描述
  String get selectionDescription {
    if (!_isLoaded) return '状态未加载';
    if (!_hasSelectedGameCircle) return '未选择游戏圈';
    return '已选择: $_selectedGameCircleName';
  }
}