import 'package:flutter/material.dart';
import 'package:dlyz_flutter/common/dl_color.dart';
import 'package:provider/provider.dart';

import 'cache_image.dart';
import '../pages/bind/switch_bind_character_page.dart';
import '../providers/user_provider.dart';

/// 游戏角色绑定底部弹窗
class GameCharacterBindingDialog extends StatefulWidget {
  final List<GameCharacter> characters;
  final ValueChanged<GameCharacter>? onCharacterSelected;
  final VoidCallback? onBindOtherCharacter;
  final VoidCallback? onClose;
  final bool isNewGift;
  final bool isCurrentAccount;

  const GameCharacterBindingDialog({
    super.key,
    required this.characters,
    this.onCharacterSelected,
    this.onBindOtherCharacter,
    this.onClose,
    this.isNewGift = false,
    this.isCurrentAccount = false,
  });

  /// 显示游戏角色绑定弹窗
  static void show({
    required BuildContext context,
    required List<GameCharacter> characters,
    ValueChanged<GameCharacter>? onCharacterSelected,
    VoidCallback? onBindOtherCharacter,
    VoidCallback? onClose,
    bool isNewGift = false,
    bool isCurrentAccount = false
  }) {
    showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => GameCharacterBindingDialog(
        characters: characters,
        onCharacterSelected: onCharacterSelected,
        onBindOtherCharacter: onBindOtherCharacter,
        onClose: onClose,
        isNewGift: isNewGift,
        isCurrentAccount: isCurrentAccount,
      ),
    ).then((result) {
      // 当dialog被关闭时，如果不是通过特定按钮关闭的，则触发onClose回调
      // result为null表示是通过点击外部区域或返回键关闭的
      if (result == null) {
        onClose?.call();
      }
    });
  }

  @override
  State<GameCharacterBindingDialog> createState() => _GameCharacterBindingDialogState();
}

class _GameCharacterBindingDialogState extends State<GameCharacterBindingDialog> {
  int? selectedIndex;
  List<GameCharacter> characters = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeCharacters();
  }

  /// 初始化角色数据
  void _initializeCharacters() {
    setState(() {
      characters = widget.characters;
      // 找到已选择的角色的索引
      selectedIndex = characters.indexWhere((char) => char.isSelected);
      if (selectedIndex == -1) {
        selectedIndex = null;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          if (isLoading)
            _buildLoadingState()
          else if (characters.isEmpty)
            _buildEmptyState()
          else if (widget.isNewGift)
            _buildNewGiftLayout()
          else if (widget.isCurrentAccount)
            _buildCurrentLayout()
          else
            _buildCharacterList(),
          if (!widget.isCurrentAccount)
            _buildBindOtherButton(),
          SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.only(top: 20, bottom: 10, left: 20, right: 20),
      child: Column(
        children: [
          // 顶部指示条
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: DLColor.divider,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          // 标题
          const Text(
            '游戏角色绑定列表',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w400,
              color: DLColor.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          if (widget.isNewGift || widget.isCurrentAccount) ...[
            const SizedBox(height: 5),
            Container(
              alignment: Alignment.centerLeft,
              margin: const EdgeInsets.only(left: 5),
              child: Consumer<UserProvider>(
                builder: (context, userProvider, child) {
                  final accountName = userProvider.currentUser?.loginInfo.muname ?? '';
                  return Text(
                    '账号$accountName',
                    style: const TextStyle(
                      fontSize: 17,
                      color: Color(0xFF3D3D3D),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建新手礼包布局
  Widget _buildNewGiftLayout() {
    // 分离当前账号和非当前账号的角色
    final currentCharacters = characters.where((char) => char.isCurrent).toList();
    final nonCurrentCharacters = characters.where((char) => !char.isCurrent).toList();
    
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.5,
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // 当前账号角色列表
            if (currentCharacters.isNotEmpty)
              _buildCharacterSection(currentCharacters, false),
            
            // 中间提示文字
            if (nonCurrentCharacters.isNotEmpty)
              _buildNewGiftNotice(),
            
            // 非当前账号角色列表（灰色显示）
            if (nonCurrentCharacters.isNotEmpty)
              _buildCharacterSection(nonCurrentCharacters, true),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentLayout() {
    // 分离当前账号和非当前账号的角色
    final currentCharacters = characters.where((char) => char.isCurrent).toList();
    final nonCurrentCharacters = characters.where((char) => !char.isCurrent).toList();
    
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.5,
      ),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // 当前账号角色列表
            if (currentCharacters.isNotEmpty)
              _buildCharacterSection(currentCharacters, false),
            
            // 中间提示文字
            _buildCurrentAccountNotice(),

          ],
        ),
      ),
    );
  }

  /// 构建角色列表段落
  Widget _buildCharacterSection(List<GameCharacter> sectionCharacters, bool isGrayedOut) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: sectionCharacters.map((character) {
          final index = characters.indexOf(character);
          return _buildCharacterItem(character, index, isGrayedOut);
        }).toList(),
      ),
    );
  }

  /// 构建新手礼包提示
  Widget _buildNewGiftNotice() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 11, vertical: 8),
      padding: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        // color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '新手礼包仅支持领取到登录APP账号游戏内绑定角色中',
        style: TextStyle(
          fontSize: 15,
          color: Color(0xFF999999),
        ),
      ),
    );
  }

  Widget _buildCurrentAccountNotice() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 11, vertical: 8),
      padding: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        // color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '仅支持兑换到登录APP账号游戏内绑定角色中',
        style: TextStyle(
          fontSize: 15,
          color: Color(0xFF999999),
        ),
      ),
    );
  }
  /// 构建加载状态
  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 40),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(DLColor.primary),
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 40),
      child: const Center(
        child: Text(
          '暂无绑定角色',
          style: TextStyle(
            fontSize: 16,
            color: DLColor.textThird,
          ),
        ),
      ),
    );
  }

  /// 构建角色列表
  Widget _buildCharacterList() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.5,
      ),
      child: ListView.builder(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: characters.length,
        itemBuilder: (context, index) {
          return _buildCharacterItem(characters[index], index);
        },
      ),
    );
  }

  /// 构建单个角色项
  Widget _buildCharacterItem(GameCharacter character, int index, [bool? forceGrayedOut]) {
    final isCurrentCharacter = character.isSelected;
    final isGrayedOut = forceGrayedOut ?? (widget.isNewGift && !character.isCurrent);
    final isClickable = !isGrayedOut;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedIndex = index;
        });
        widget.onCharacterSelected?.call(character);
      } ,
      child: Opacity(
        opacity: isGrayedOut ? 0.5 : 1.0,
        child: Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Color(0xFFF6F7F9),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Row(
            children: [
              // 角色头像
              ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: CachedImage(
                  imageUrl: character.avatar,
                  width: 48,
                  height: 48,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),
              // 角色信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      character.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: isGrayedOut ? const Color(0xFF999999) : DLColor.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${character.server}  ${character.level}级',
                      style: TextStyle(
                        fontSize: 14,
                        color: isGrayedOut ? const Color(0xFF999999) : DLColor.textThird,
                      ),
                    ),
                  ],
                ),
              ),
              // 当前角色标识或切换按钮或非当前账号角色
              if (isCurrentCharacter)
                Container(
                  width: 75,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(29, 111, 233, 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '当前角色',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 13,
                      color: Color(0xFF4571FB),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              else if (isGrayedOut)
                Container(
                  width: 100,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: const Text(
                    '非当前账号角色',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF999999),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                )
              else
                Container(
                  width: 75,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: Color(0xFF4571FB),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '切换',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建绑定其他角色按钮
  Widget _buildBindOtherButton() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: GestureDetector(
        onTap: () async {
          // 先关闭当前弹窗
          if (mounted) {
            Navigator.pop(context, 'bind_other');
            widget.onClose?.call();
          }
          // 跳转到SwitchBindCharacterPage页面
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const SwitchBindCharacterPage(),
            ),
          );
          
        },
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: DLColor.border,
              width: 1,
            ),
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                color: DLColor.textSecondary,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                '绑定其他角色',
                style: TextStyle(
                  fontSize: 16,
                  color: DLColor.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 游戏角色数据模型
class GameCharacter {
  final String id;
  final String name;
  final String server;
  final String level;
  final String avatar;
  final bool isSelected;
  final bool isCurrent;

  const GameCharacter({
    required this.id,
    required this.name,
    required this.server,
    required this.level,
    required this.avatar,
    this.isSelected = false,
    this.isCurrent = true,
  });

  GameCharacter copyWith({
    String? id,
    String? name,
    String? server,
    String? level,
    String? avatar,
    bool? isSelected,
    bool? isCurrent,
  }) {
    return GameCharacter(
      id: id ?? this.id,
      name: name ?? this.name,
      server: server ?? this.server,
      level: level ?? this.level,
      avatar: avatar ?? this.avatar,
      isSelected: isSelected ?? this.isSelected,
      isCurrent: isCurrent ?? this.isCurrent,
    );
  }
}
