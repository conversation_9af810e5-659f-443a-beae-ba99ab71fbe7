import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../model/emoji.dart';
import '../model/forum_post_list.dart';
import '../components/cache_image.dart';
import '../components/video_player_card.dart';
import '../components/vote_widget.dart';
import '../info/forum_info.dart';

/// 论坛帖子列表项组件
/// 提供统一的帖子显示样式，可在论坛列表页和搜索页复用
class ForumPostItem extends StatelessWidget {
  final ForumPost post;
  final int index;
  final bool isFirstItem;
  final bool showVoteWidget;
  final Set<int> followingUserIds;
  final Set<int> followLoadingUserIds;
  final VoidCallback? onTap;
  final Function(ForumUser)? onUserTap;
  final Function(ForumUser)? onFollowTap;
  final Function(String)? onTopicTap;
  final Function(String)? onVideoTap;
  final Function(BuildContext, List<dynamic>, int, {ForumPost? post})? onImageGalleryTap;
  final Function(ForumPost, VoidCallback?)? onVoteSuccess;
  final Function(int, int)? onRefreshPostDetail;
  final EdgeInsets? contentPadding;

  const ForumPostItem({
    super.key,
    required this.post,
    required this.index,
    this.isFirstItem = false,
    this.showVoteWidget = true,
    this.followingUserIds = const {},
    this.followLoadingUserIds = const {},
    this.onTap,
    this.onUserTap,
    this.onFollowTap,
    this.onTopicTap,
    this.onVideoTap,
    this.onImageGalleryTap,
    this.onVoteSuccess,
    this.onRefreshPostDetail,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 灰色分割线 - 根据条件显示
        if (!isFirstItem)
          Container(
            height: 8,
            color: Colors.grey[200],
            width: double.infinity,
          ),
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部用户信息区域
                Padding(
                  padding: contentPadding ?? EdgeInsets.zero,
                  child: _buildUserHeader(context),
                ),

                const SizedBox(height: 12),

                // 帖子标题
                if (post.title.isNotEmpty)
                  Padding(
                    padding: contentPadding ?? EdgeInsets.zero,
                    child: _buildTextWithEmojis(
                      post.title,
                      textStyle: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                const SizedBox(height: 8),

                // 帖子内容预览
                Padding(
                  padding: contentPadding ?? EdgeInsets.zero,
                  child: _buildContentPreview(context),
                ),

                const SizedBox(height: 12),

                // 根据是否有投票模块决定话题标签的位置
                if (post.voteId == null || !showVoteWidget) 
                  Padding(
                    padding: contentPadding ?? EdgeInsets.zero,
                    child: _buildTopicTags(context),
                  ),

                // 投票模块（根据vote_id判断是否显示）
                if (post.voteId != null && showVoteWidget)
                  VoteWidget(
                    post: post,
                    displayType: VoteDisplayType.list,
                    onVoteSuccess: (updatedPost) {
                      if (onVoteSuccess != null) {
                        onVoteSuccess!(updatedPost, () async {
                          // 投票成功后重新获取该帖子的详情来刷新投票数据
                          if (onRefreshPostDetail != null) {
                            await onRefreshPostDetail!(post.threadId, index);
                          }
                        });
                      }
                    },
                  ),

                // 如果有投票模块，话题标签显示在投票模块之后，并添加间距
                if (post.voteId != null && showVoteWidget) ...[
                  const SizedBox(height: 12),
                  Padding(
                    padding: contentPadding ?? EdgeInsets.zero,
                    child: _buildTopicTags(context),
                  ),
                ],

                // 底部信息栏上方增加间距
                const SizedBox(height: 12),

                // 底部信息栏
                Padding(
                  padding: contentPadding ?? EdgeInsets.zero,
                  child: _buildBottomInfo(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserHeader(BuildContext context) {
    return Row(
      children: [
        // 用户头像
        GestureDetector(
          onTap: () => onUserTap?.call(post.user),
          child: Stack(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.grey[200],
                backgroundImage: post.user.avatar.isNotEmpty 
                    ? NetworkImage(post.user.avatar) 
                    : null,
                child: post.user.avatar.isEmpty 
                    ? Icon(
                        Icons.person,
                        size: 20,
                        color: Colors.grey[600],
                      )
                    : null,
              ),
              // Badge角标（根据接口返回的badge字段显示）
              if (post.user.badge.isNotEmpty)
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: post.user.badge,
                        width: 14,
                        height: 14,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[300],
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.error,
                            size: 8,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户名和奖章
              Row(
                children: [
                  Flexible(
                    child: Text(
                      post.user.nickname.isNotEmpty ? post.user.nickname : '匿名用户',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // 奖章图标
                  if (_parseMedals(post.user.medal).isNotEmpty) ...[
                    const SizedBox(width: 6),
                    _buildMedalIcons(_parseMedals(post.user.medal)),
                  ],
                ],
              ),
              
              const SizedBox(height: 2),
              
              // 用户标签（根据接口的label和color字段显示）
              if (post.user.label.isNotEmpty)
                Text(
                  post.user.label,
                  style: TextStyle(
                    fontSize: 10,
                    color: _parseColor(post.user.color),
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
        
        // 关注按钮 - 只有未关注且不在关注中时才显示
        if (!followingUserIds.contains(post.user.userId) && !followLoadingUserIds.contains(post.user.userId))
          GestureDetector(
            onTap: () => onFollowTap?.call(post.user),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFF4571FB), width: 1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Text(
                '+关注',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF4571FB),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        // 关注中状态
        if (followLoadingUserIds.contains(post.user.userId))
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[400] ?? Colors.grey, width: 1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildContentPreview(BuildContext context) {
    final String cleanText = _stripHtmlTags(post.content.text);
    final bool needsTruncation = cleanText.length > 102;
    final String displayText = needsTruncation 
        ? '${cleanText.substring(0, 102)}...' 
        : cleanText;

    // 获取所有图片（包括content.images和图文分离的图片）
    List<dynamic> allImages = _getAllImages(post);
    
    // 检查是否有视频 - 通过索引108判断
    bool hasVideo = _hasVideo(post);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 显示文本内容
        if (displayText.isNotEmpty)
          _buildTextWithEmojis(
            displayText,
            textStyle: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        
        // 显示全文按钮（只在文本被截断时显示）
        if (needsTruncation) ...[
          const SizedBox(height: 8),
          Text(
            '全文',
            style: TextStyle(
              fontSize: 14,
              color: Colors.blue[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
        
        // 优先显示视频，如果没有视频则显示图片
        if (hasVideo) ...[
          const SizedBox(height: 8),
          _buildVideoThumbnailFromIndex(post),
        ] else if (allImages.isNotEmpty) ...[
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => onImageGalleryTap?.call(context, allImages, 0, post: post),
            child: _buildContentImages(allImages, post: post),
          ),
        ],
      ],
    );
  }

  /// 获取帖子中所有图片（包括content.images和图文分离的图片）
  /// 如果帖子有视频，则不返回图片（视频优先显示）
  List<dynamic> _getAllImages(ForumPost post) {
    List<dynamic> allImages = [];
    
    // 如果帖子有视频，则不返回图片，实现视频优先显示
    if (_hasVideo(post)) {
      return allImages; // 返回空列表
    }
    
    // 先添加content.images中的图片
    if (post.content.images.isNotEmpty) {
      allImages.addAll(post.content.images);
    }
    
    // 判断是否是图文分离的帖子
    final bool isSeparatedContent = post.from != 2 && post.isMixThread != 1;
    
    if (isSeparatedContent) {
      // 从indexes[101]获取图文分离的图片
      final indexes = post.content.indexes;
      
      if (indexes.containsKey('101')) {
        final imageIndex = indexes['101'];
        
        if (imageIndex is Map<String, dynamic> && imageIndex['body'] is List) {
          final bodyList = imageIndex['body'] as List;
          
          for (var item in bodyList) {
            if (item is Map<String, dynamic>) {
              final imageUrl = item['url'] ?? item['thumbUrl'] ?? item['attachment'];
              if (imageUrl != null && imageUrl.isNotEmpty) {
                allImages.add(item);
              }
            }
          }
        }
      }
    }
    
    return allImages;
  }

  Widget _buildContentImages(List<dynamic> images, {ForumPost? post}) {
    if (images.isEmpty) return const SizedBox.shrink();
    
    // 最多显示2张图片
    final int displayCount = images.length > 2 ? 2 : images.length;
    
    if (displayCount == 1) {
      // 单张图片
      return _buildSingleImage(images[0], allImages: images, post: post);
    } else {
      // 多张图片网格
      return _buildImageGrid(images, displayCount, post: post);
    }
  }

  Widget _buildSingleImage(dynamic imageData, {List<dynamic>? allImages, ForumPost? post}) {
    String? imageUrl;
    
    // 尝试从不同字段获取图片URL
    if (imageData is Map<String, dynamic>) {
      imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      imageUrl = imageData;
    }
    
    if (imageUrl == null || imageUrl.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedImage(
          imageUrl: imageUrl,
          width: double.infinity,
          height: 200,
          fit: BoxFit.cover,
          onTap: () => onImageGalleryTap?.call(null as BuildContext, allImages ?? [imageData], 0, post: post),
        ),
      ),
    );
  }

  Widget _buildImageGrid(List<dynamic> images, int displayCount, {ForumPost? post}) {
    return SizedBox(
      height: 120,
      child: Row(
        children: List.generate(displayCount, (index) {
          final imageData = images[index];
          String? imageUrl;
          
          if (imageData is Map<String, dynamic>) {
            imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
          } else if (imageData is String) {
            imageUrl = imageData;
          }
          
          return Expanded(
            child: GestureDetector(
              onTap: () => onImageGalleryTap?.call(null as BuildContext, images, index, post: post),
              child: Container(
                margin: EdgeInsets.only(right: index < (displayCount - 1) ? 4 : 0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: imageUrl != null && imageUrl.isNotEmpty
                          ? CachedImage(
                              imageUrl: imageUrl,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color: Colors.grey[200],
                              child: Icon(
                                Icons.image,
                                color: Colors.grey[600],
                              ),
                            ),
                    ),
                    
                    // 如果是最后一张图片且还有更多图片，显示"+N"标识
                    if (index == (displayCount - 1) && images.length > displayCount)
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: Colors.black.withValues(alpha: 0.5),
                        ),
                        child: Center(
                          child: Text(
                            '+${images.length - displayCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildTopicTags(BuildContext context) {
    // 如果没有话题标签，返回空Widget
    if (post.topics.trim().isEmpty) {
      return const SizedBox.shrink();
    }

    List<Map<String, String>> topicList = _parseTopics(post.topics);

    if (topicList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 8.0,
          runSpacing: 4.0,
          children: topicList.map((topicData) {
            return GestureDetector(
              onTap: () => onTopicTap?.call(topicData['id'] ?? topicData['content'] ?? ''),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '#${topicData['content'] ?? topicData['id'] ?? ''}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  List<Map<String, String>> _parseTopics(String topicsStr) {
    List<Map<String, String>> result = [];

    try {
      // 尝试解析为JSON
      dynamic parsed = json.decode(topicsStr);

      if (parsed is List) {
        // JSON数组格式: [{"id": "1", "content": "游戏"}, {"id": "2", "content": "攻略"}]
        for (var item in parsed) {
          if (item is Map<String, dynamic>) {
            result.add({
              'id': item['id']?.toString() ?? '',
              'content': item['content']?.toString() ?? '',
            });
          }
        }
      } else if (parsed is Map<String, dynamic>) {
        // 单个JSON对象格式: {"id": "1", "content": "游戏"}
        result.add({
          'id': parsed['id']?.toString() ?? '',
          'content': parsed['content']?.toString() ?? '',
        });
      }
    } catch (e) {
      // JSON解析失败，尝试作为简单字符串处理
      List<String> simpleTopics = topicsStr
          .split(RegExp(r'[,，\s]+'))
          .where((topic) => topic.trim().isNotEmpty)
          .map((topic) => topic.trim())
          .toList();

      for (String topic in simpleTopics) {
        result.add({
          'id': topic,
          'content': topic,
        });
      }
    }

    return result;
  }

  Widget _buildBottomInfo() {
    return Row(
      children: [
        // 发布时间
        Text(
          _formatTimeAgo(post.createdAt),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
        
        const Spacer(),
        
        // 浏览量
        Icon(
          Icons.visibility_outlined,
          size: 16,
          color: Colors.grey[500],
        ),
        const SizedBox(width: 4),
        Text(
          _formatCount(post.viewCount),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }

  String _formatTimeAgo(String createdAt) {
    try {
      final DateTime postTime = DateTime.parse(createdAt);
      final DateTime now = DateTime.now();
      final Duration diff = now.difference(postTime);
      
      if (diff.inMinutes < 60) {
        return '${diff.inMinutes}分钟前';
      } else if (diff.inHours < 24) {
        return '${diff.inHours}小时前';
      } else {
        // 超过1天直接显示日期时间
        return '${postTime.year}-${postTime.month.toString().padLeft(2, '0')}-${postTime.day.toString().padLeft(2, '0')} ${postTime.hour.toString().padLeft(2, '0')}:${postTime.minute.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return '刚刚';
    }
  }

  String _formatCount(int count) {
    if (count > 10000) {
      return '${(count / 10000).toStringAsFixed(1)}w';
    } else if (count > 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }

  /// 构建带表情包的文本Widget
  Widget _buildTextWithEmojis(String text, {TextStyle? textStyle, int? maxLines, TextOverflow? overflow}) {
    final emojis = ForumInfo().emojis;
    
    if (emojis.isEmpty || !text.contains(':')) {
      return Text(text, style: textStyle, maxLines: maxLines, overflow: overflow);
    }

    final List<InlineSpan> spans = [];
    final regex = RegExp(r':([^:]+):');
    int currentIndex = 0;
    
    for (final match in regex.allMatches(text)) {
      // 添加表情前的文本
      if (match.start > currentIndex) {
        spans.add(TextSpan(
          text: text.substring(currentIndex, match.start),
          style: textStyle,
        ));
      }
      
      // 查找表情包 - 直接遍历emojis列表查找匹配的code
      final emojiCode = match.group(1);
      final fullEmojiCode = ":${emojiCode}:";
      Emoji? foundEmoji;
      
      for (final emoji in emojis) {
        if (emoji.code == fullEmojiCode) {
          foundEmoji = emoji;
          break;
        }
      }
      
      if (foundEmoji != null && foundEmoji.url.isNotEmpty) {
        // 添加表情包图片
        spans.add(WidgetSpan(
          alignment: PlaceholderAlignment.bottom,
          child: Container(
            width: 18,
            height: 18,
            child: CachedNetworkImage(
              imageUrl: foundEmoji.url,
              width: 18,
              height: 18,
              fit: BoxFit.contain,
              placeholder: (context, url) => Text(':$emojiCode:', style: textStyle),
              errorWidget: (context, url, error) {
                return Text(':$emojiCode:', style: textStyle);
              },
            ),
          ),
        ));
      } else {
        // 如果找不到表情包，保持原文本
        spans.add(TextSpan(
          text: ':$emojiCode:',
          style: textStyle,
        ));
      }
      
      currentIndex = match.end;
    }
    
    // 添加剩余文本
    if (currentIndex < text.length) {
      spans.add(TextSpan(
        text: text.substring(currentIndex),
        style: textStyle,
      ));
    }
    
    return Text.rich(
      TextSpan(children: spans),
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  String _stripHtmlTags(String html) {
    if (html.isEmpty) return '';
    
    // 移除HTML标签
    String result = html.replaceAll(RegExp(r'<[^>]*>', multiLine: true, caseSensitive: false), '');
    
    // 替换HTML实体
    result = result
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&amp;', '&')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&mdash;', '—')
        .replaceAll('&ldquo;', '"')
        .replaceAll('&rdquo;', '"');
    
    // 移除多余的空白字符和换行
    result = result
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    return result;
  }

  /// 检查帖子是否有视频 - 支持新格式(content.videos)和旧格式(索引108)
  bool _hasVideo(ForumPost post) {
    // 优先检查新格式：content.videos
    if (post.content.videos.isNotEmpty) {
      return true;
    }
    
    // 检查旧格式：索引108
    final indexes = post.content.indexes;
    if (indexes.containsKey('108')) {
      final videoIndex = indexes['108'];
      if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
        final bodyList = videoIndex['body'] as List;
        return bodyList.isNotEmpty;
      }
    }
    return false;
  }

  /// 构建视频缩略图 - 支持新格式(content.videos + covers)和旧格式(索引108)
  /// 如果没有封面，则生成视频缩略图（与VideoPlayerCard保持一致）
  Widget _buildVideoThumbnailFromIndex(ForumPost post) {
    String? coverUrl;
    String? videoUrl;
    
    // 优先使用新格式：content.videos + covers
    if (post.content.videos.isNotEmpty) {
      videoUrl = post.content.videos.first;
      // 获取视频封面：优先使用covers，如果没有则使用默认图标
      if (post.content.covers.isNotEmpty) {
        coverUrl = post.content.covers.first['url'];
      }
    } else {
      // 使用旧格式：索引108
      final indexes = post.content.indexes;
      if (indexes.containsKey('108')) {
        final videoIndex = indexes['108'];
        if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
          final bodyList = videoIndex['body'] as List;
          if (bodyList.isNotEmpty) {
            final videoData = bodyList.first;
            if (videoData is Map<String, dynamic>) {
              videoUrl = videoData['url'];
              // 从cover字段获取视频预览图
              if (videoData['cover'] is Map<String, dynamic>) {
                coverUrl = videoData['cover']['url'];
              } else {
                // 备用方案：使用thumbUrl
                coverUrl = videoData['thumbUrl'];
              }
            }
          }
        }
      }
    }
    
    // 如果没有封面URL，则使用VideoPlayerCard来处理缩略图生成
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: VideoPlayerCard(
          videoUrl: videoUrl ?? '',
          coverUrl: coverUrl,
          autoPlay: false,
          playOnTap: false, // 不允许点击播放，因为我们有自己的点击处理
          height: 200,
          margin: EdgeInsets.zero,
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            // 获取视频URL并直接全屏播放
            String? finalVideoUrl;
            
            // 优先使用新格式：content.videos
            if (post.content.videos.isNotEmpty) {
              finalVideoUrl = post.content.videos.first;
            } else {
              // 使用旧格式：索引108
              final indexes = post.content.indexes;
              if (indexes.containsKey('108')) {
                final videoIndex = indexes['108'];
                if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
                  final bodyList = videoIndex['body'] as List;
                  if (bodyList.isNotEmpty && bodyList.first is Map<String, dynamic>) {
                    final videoData = bodyList.first as Map<String, dynamic>;
                    finalVideoUrl = videoData['url'];
                  }
                }
              }
            }
            
            if (finalVideoUrl != null && finalVideoUrl.isNotEmpty) {
              onVideoTap?.call(finalVideoUrl);
            } else {
              // 如果获取不到视频URL，则跳转到详情页
              onTap?.call();
            }
          },
        ),
      ),
    );
  }

  /// 解析奖章字符串并返回有效的URL列表
  List<String> _parseMedals(dynamic medal) {
    if (medal == null) return [];
    
    List<String> medalUrls = [];
    
    try {
      if (medal is String) {
        if (medal.trim().isEmpty) return [];
        
        // 尝试解析为JSON数组
        try {
          final dynamic parsed = json.decode(medal);
          if (parsed is List) {
            for (var item in parsed) {
              if (item is String && _isValidUrl(item)) {
                medalUrls.add(item);
              }
            }
          }
        } catch (e) {
          // 如果不是JSON，检查是否是单个URL
          if (_isValidUrl(medal)) {
            medalUrls.add(medal);
          }
        }
      } else if (medal is List) {
        for (var item in medal) {
          if (item is String && _isValidUrl(item)) {
            medalUrls.add(item);
          }
        }
      }
    } catch (e) {
      // 解析失败，返回空列表
      return [];
    }
    
    return medalUrls;
  }
  
  /// 验证URL格式是否正确
  bool _isValidUrl(String url) {
    if (url.trim().isEmpty) return false;
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// 构建奖章图标
  Widget _buildMedalIcons(List<String> medalUrls) {
    if (medalUrls.isEmpty) return const SizedBox.shrink();
    
    return Wrap(
      spacing: 4,
      children: medalUrls.take(3).map((url) { // 最多显示3个奖章
        return Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.grey[300] ?? Colors.grey, width: 0.5),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: CachedNetworkImage(
              imageUrl: url,
              width: 20,
              height: 20,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey[200],
                child: Icon(
                  Icons.emoji_events,
                  size: 12,
                  color: Colors.grey[400],
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[200],
                child: Icon(
                  Icons.error,
                  size: 12,
                  color: Colors.grey[400],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 解析颜色字符串（支持#RRGGBB格式）
  Color _parseColor(String colorString) {
    if (colorString.isEmpty) {
      return Colors.blue; // 默认颜色
    }
    
    try {
      // 移除可能存在的#号
      String cleanColor = colorString.replaceAll('#', '');
      
      // 确保是6位十六进制
      if (cleanColor.length == 6) {
        return Color(int.parse('FF$cleanColor', radix: 16));
      }
      
      // 如果是8位，包含透明度
      if (cleanColor.length == 8) {
        return Color(int.parse(cleanColor, radix: 16));
      }
      
      return Colors.blue; // 解析失败时的默认颜色
    } catch (e) {
      return Colors.blue; // 解析失败时的默认颜色
    }
  }
}