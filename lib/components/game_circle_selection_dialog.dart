import 'package:flutter/material.dart';
import '../model/game_circle.dart';
import '../track/circle_track.dart';
import '../net/api/game_circle_service.dart';
import '../utils/log_util.dart';

class GameCircleSelectionDialog extends StatefulWidget {
  final String title;
  final List<GameCircle>? gameCircles;
  final Function(GameCircle)? onCircleSelected;
  final VoidCallback? onClose;
  final bool autoLoad; // 是否自动加载数据

  const GameCircleSelectionDialog({
    super.key,
    required this.title,
    this.gameCircles,
    this.onCircleSelected,
    this.onClose,
    this.autoLoad = false,
  });

  // 显示底部弹窗的静态方法
  static void show({
    required BuildContext context,
    required String title,
    List<GameCircle>? gameCircles,
    Function(GameCircle)? onCircleSelected,
    VoidCallback? onClose,
    bool autoLoad = false,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => GameCircleSelectionDialog(
        title: title,
        gameCircles: gameCircles,
        onCircleSelected: onCircleSelected,
        onClose: onClose,
        autoLoad: autoLoad,
      ),
    );
  }

  @override
  State<GameCircleSelectionDialog> createState() => _GameCircleSelectionDialogState();
}

class _GameCircleSelectionDialogState extends State<GameCircleSelectionDialog> {
  List<GameCircle>? _gameCircles;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _gameCircles = widget.gameCircles;
    
    if (widget.autoLoad && _gameCircles == null) {
      _loadGameCircles();
    }
    
    // 如果已有数据，触发埋点
    if (_gameCircles != null && _gameCircles!.isNotEmpty) {
      _trackGameCircleListShow();
    }
  }
  
  void _trackGameCircleListShow() {
    // 游戏圈集合页曝光埋点
    CircleTrack.trackGameCircleListShow(extraData: {
      'circle_count': _gameCircles?.length ?? 0,
      'page_type': 'GameCircleSelectionDialog',
      'dialog_title': widget.title,
    });
  }
  
  Future<void> _loadGameCircles() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    
    // 记录开始时间，确保最小加载时间
    final startTime = DateTime.now();
    const minLoadingDuration = Duration(milliseconds: 800); // 最小加载时间800ms
    
    try {
      LogUtil.d('开始请求游戏圈子列表...');
      
      final response = await GameCircleService.getGameCircleList(
        context: context,
      );
      
      // 计算已经过去的时间
      final elapsed = DateTime.now().difference(startTime);
      final remainingTime = minLoadingDuration - elapsed;
      
      // 如果还没到最小加载时间，等待剩余时间
      if (remainingTime.inMilliseconds > 0) {
        await Future.delayed(remainingTime);
      }
      
      if (response.success && response.data != null) {
        final gameCircles = response.data!.circles;
        LogUtil.d('获取到 ${gameCircles.length} 个游戏圈子');
        
        if (mounted) {
          setState(() {
            _gameCircles = gameCircles;
            _isLoading = false;
          });
          
          // 数据加载完成后触发埋点
          _trackGameCircleListShow();
        }
      } else {
        LogUtil.e('获取游戏圈子列表失败: ${response.message}');
        if (mounted) {
          setState(() {
            _errorMessage = response.message ?? '获取游戏圈子列表失败';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      LogUtil.e('请求游戏圈子列表异常: $e');
      
      // 计算已经过去的时间
      final elapsed = DateTime.now().difference(startTime);
      final remainingTime = minLoadingDuration - elapsed;
      
      // 如果还没到最小加载时间，等待剩余时间
      if (remainingTime.inMilliseconds > 0) {
        await Future.delayed(remainingTime);
      }
      
      if (mounted) {
        setState(() {
          _errorMessage = '网络异常: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 固定高度为屏幕的一半，数据过多时可以滑动
    final screenHeight = MediaQuery.of(context).size.height;
    final fixedHeight = screenHeight * 0.6; // 固定为50%屏幕高度
    
    return Container(
      height: fixedHeight, // 使用固定高度
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max, // 改为max，占满容器
        children: [
          // 顶部标题栏
          _buildHeader(context),
          
          // 游戏圈子选择列表 - 使用Expanded占满剩余空间
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 居中标题
          Center(
            child: Text(
              widget.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),

        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return _buildLoadingState();
    } else if (_errorMessage != null) {
      return _buildErrorState();
    } else {
      return _buildGameCircleList();
    }
  }
  
  Widget _buildLoadingState() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              '加载游戏圈列表中...',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildErrorState() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadGameCircles,
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameCircleList() {
    if (_gameCircles == null || _gameCircles!.isEmpty) {
      return Container(
        width: double.infinity,
        color: Colors.white,
        child: const Center(
          child: Text(
            '暂无可选择的游戏圈',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      width: double.infinity,
      color: Colors.white,
      child: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
        child: GridView.builder(
          shrinkWrap: true, // 让GridView只占用实际需要的高度
          physics: const NeverScrollableScrollPhysics(), // 禁用GridView自身的滚动，使用外层的SingleChildScrollView
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.85,
          ),
          itemCount: _gameCircles!.length,
          itemBuilder: (context, index) {
            return _buildGameCircleItem(_gameCircles![index], context);
          },
        ),
      ),
    );
  }

  Widget _buildGameCircleItem(GameCircle gameCircle, BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 处理游戏圈选择
        print('选择了游戏圈: ${gameCircle.displayName}');
        
        // 点击选择游戏圈子埋点
        CircleTrack.trackChooseGameCircle(
          tgid: gameCircle.tgid,
          circleName: gameCircle.displayName,
          extraData: {
            'circle_id': gameCircle.circleId,
            'source': 'GameCircleSelectionDialog',
          },
        );
        
        Navigator.pop(context);
        if (widget.onCircleSelected != null) {
          widget.onCircleSelected!(gameCircle);
        }
        if (widget.onClose != null) {
          widget.onClose!();
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 游戏圈图标
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                gameCircle.circleIcon,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.gamepad,
                      color: Colors.grey,
                      size: 40,
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 8),
          
          // 游戏圈名称
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Text(
                gameCircle.displayName,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 便捷显示方法
class GameCircleSelectionHelper {
  static void showGameCircleSelectionDialog(
    BuildContext context, {
    required String title,
    List<GameCircle>? gameCircles,
    Function(GameCircle)? onCircleSelected,
    VoidCallback? onClose,
    bool autoLoad = false,
  }) {
    GameCircleSelectionDialog.show(
      context: context,
      title: title,
      gameCircles: gameCircles,
      onCircleSelected: onCircleSelected,
      onClose: onClose,
      autoLoad: autoLoad,
    );
  }
}