import 'package:dlyz_flutter/components/download_bottom_dialog.dart';
import 'package:dlyz_flutter/providers/download_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:dlyz_flutter/manager/game_data_manager.dart';
import 'dart:convert';
import 'dart:io';
import '../../components/select_game_bottom_dialog.dart';
import '../../model/package_info.dart';
import '../../model/game_post_list.dart';
import '../../model/game_binding_info.dart';
import '../../providers/user_provider.dart';
import '../../providers/game_circle_provider.dart';
import '../../services/bind_account_service.dart';
import '../../config/app_config.dart';
import '../../manager/channel_manager.dart';
import '../../utils/log_util.dart';
import '../track/circle_track.dart';
import 'cache_image.dart';

/// 游戏授权绑定底部弹窗
class GameAuthorizationDialog extends StatefulWidget {
  final VoidCallback? onClose;
  final String? traceId;

  const GameAuthorizationDialog({
    super.key,
    this.onClose,
    this.traceId,
  });

  /// 显示游戏授权绑定弹窗
  static void show({
    required BuildContext context,
    VoidCallback? onClose,
    String? traceId,
  }) {
    showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => GameAuthorizationDialog(
        onClose: onClose,
        traceId: traceId,
      ),
    ).then((result) {
      // 当dialog被关闭时，如果不是通过特定按钮关闭的，则触发onClose回调
      if (result == null) {
        onClose?.call();
      }
    });
  }

  @override
  State<GameAuthorizationDialog> createState() => _GameAuthorizationDialogState();
}

class _GameAuthorizationDialogState extends State<GameAuthorizationDialog> {
  List<PackageInfo> _packages = [];
  int tGid = 0;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadGamePackages();
  }

  /// Load game packages and convert PackageInfoItem to PackageInfo
  Future<void> _loadGamePackages() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final gameCircleProvider = Provider.of<GameCircleProvider>(
        context,
        listen: false,
      );

      if (!gameCircleProvider.hasSelectedGameCircle ||
          gameCircleProvider.selectedGameCircle == null) {
        throw Exception('No selected game circle');
      }

      final selectedCircle = gameCircleProvider.selectedGameCircle!;
      tGid = int.parse(selectedCircle.tgid);

      LogUtil.d('Starting to get game package info, tgid: $tGid');

      // Call bind account service API to get package info
      final bindAccountService = BindAccountService();
      final response = await bindAccountService.getPackageInfo(
        pid: AppConfig.pid,
        gid: AppConfig.gid,
        tgid: "$tGid",
        os: Platform.isAndroid ? 'android' : 'ios',
        packageNameList: [], // Empty list means get all available packages
      );

      if (response.success && response.data?.data?.packageInfo != null) {
        final packageItems = response.data!.data!.packageInfo;
        LogUtil.d('Game package info retrieved successfully, ${packageItems.length} packages');
        
        // Convert PackageInfoItem to PackageInfo
        final packages = packageItems.map((item) => _convertToPackageInfo(item)).toList();
        await GameDataManager().checkInstallation(packages);
        await GameDataManager().reloadInstalledGamesListIcon(packages);
        if (mounted) {
          setState(() {
            _packages = packages;
            _isLoading = false;
          });
        }
      } else {
        LogUtil.w('Failed to get game package info: ${response.message}');
        throw Exception(response.message);
      }
    } catch (e) {
      LogUtil.e('Exception loading game package info: $e');
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  /// Convert PackageInfoItem to PackageInfo
  PackageInfo _convertToPackageInfo(PackageInfoItem item) {
    PackageType packageType;
    switch (item.packageType.toLowerCase()) {
      case 'official':
        packageType = PackageType.official;
        break;
      case 'wechat':
        packageType = PackageType.wechat;
        break;
      case 'other_package':
        packageType = PackageType.otherPackage;
        break;
      default:
        packageType = PackageType.unknown;
        break;
    }

    return PackageInfo(
      id: DateTime.now().millisecondsSinceEpoch + item.packageName.hashCode,
      packageType: packageType,
      title: item.title,
      packageName: item.packageName,
      wechatMiniProgramId: item.wechatMinigramId ?? 0,
      iconUrl: item.iconUrl,
      downloadUrl: item.downloadUrl,
      floatConfig: GameFloatConfig(
        floatMsg: item.floatConfig?.showFloat ?? false,
      ),
      hasInstall: item.hasInstall,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        height: 200,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Container(
        height: 200,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.grey),
              const SizedBox(height: 8),
              Text(
                '加载失败: $_error',
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, 'cancel'),
                child: const Text('关闭'),
              ),
            ],
          ),
        ),
      );
    }

    final officialPackage = _packages.firstWhere((item) => item.packageType.isOfficial());

    if (Platform.isAndroid) {
      if (_packages.length > 1 && officialPackage.hasInstall) {
        return SelectGamePackageBottomDialog(
          isShowMiniProgram: true,
          packages: _packages,
          onConfirm: (game) {
            if (game.packageType.isWeChat()) {
              _jumpMiniProgram(game);
            } else {
              _launchGame(game);
            }
          },
          onCancel: () {
            // Dialog will close automatically
          },
        );
      } else {
        final game = GameDataManager().findExistGameDetail(tGid);
        if (game != null) {
          return Stack(
            alignment: Alignment.topCenter,
            children: [
              DownloadBottomDialog(
                  context: context,
                  game: game
              ),
              // 顶部突出的游戏图标
              Positioned(
                top: 0, // 向上突出一半
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedImage(
                      width: 80,
                      height: 80,
                      imageUrl: game.icon
                  ),
                ),
              ),
              // 关闭按钮(左侧)
              Positioned(
                left: 0,
                top: 0,
                child: IconButton(
                  icon: const Icon(Icons.keyboard_arrow_down, size: 25),
                  color: Colors.white,
                  onPressed: () => Navigator.pop(context),
                ),
              ),
            ],
          );
        } else {
          Navigator.pop(context);
        }
      }
    } else if (Platform.isIOS) {
      if (_packages.length > 1 && officialPackage.hasInstall) {
        return SelectGamePackageBottomDialog(
          isShowMiniProgram: true,
          packages: _packages,
          onConfirm: (game) {
            if (game.packageType.isWeChat()) {
              _jumpMiniProgram(game);
            } else {
              _launchGame(game);
            }
          },
          onCancel: () {
            // Dialog will close automatically
          },
        );
      } else {
        final game = GameDataManager().findExistGameDetail(tGid);
        if (game != null) {
          DownloadProvider().handleDownload(game.downloadInfo);
        }
        Navigator.pop(context);
      }
    }
    return SelectGamePackageBottomDialog(
      isShowMiniProgram: true,
      packages: _packages,
      onConfirm: (game) {
        CircleTrack.trackChooseGamePackPopShow(extraData: {'choose_game_status': game.packageType == PackageType.official ? 1 : 0});
        if (game.packageType.isWeChat()) {
          _jumpMiniProgram(game);
        } else {
          _launchGame(game);
        }
      },
      onCancel: () {
        // Dialog will close automatically
      },
    );
  }

  /// 启动游戏
  Future<void> _jumpMiniProgram(PackageInfo game) async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final ticket = userProvider.currentTicket;

      if (ticket == null || ticket.isEmpty) {
        _showErrorDialog('用户登录信息已过期，请重新登录');
        return;
      }
      
      // 使用传入的trace_id，如果没有则生成新的
      final traceId = widget.traceId ?? GameBindingInfo.generateTraceId();
      GameDataManager().requestJumpMiniProgram(
        game.wechatMiniProgramId, 
        extra: '&cq=${Uri.encodeComponent('appid=${AppConfig.appId}&app_pid=${AppConfig.pid}&app_gid=${AppConfig.gid}&trace_id=${traceId}&app_ticket=1')}',
      );
      // 不关闭弹窗，让用户可以继续操作
    } catch (e) {
      LogUtil.e('Exception launching game: $e');
      _showErrorDialog('启动游戏失败: $e');
    }
  }

  void _launchGame(PackageInfo packageInfo) async {
    
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final ticket = userProvider.currentTicket;

      if (ticket == null || ticket.isEmpty) {
        _showErrorDialog('用户登录信息已过期，请重新登录');
        return;
      }

      final traceId = widget.traceId ?? GameBindingInfo.generateTraceId();
    // Create game binding info
      final gameBindingInfo = GameBindingInfo(
        appTicket: ticket,
        appId: AppConfig.appId,
        appPid: AppConfig.pid,
        appGid: AppConfig.gid,
        traceId: traceId,
      );

      LogUtil.d("Game binding params: ${jsonEncode(gameBindingInfo.toJson())}");

      // Use ChannelManager to launch game
      await ChannelManager().bindingGame(
        packageName: packageInfo.packageName,
        gameBindingInfo: gameBindingInfo,
        gameExtParams: {
          'dialogContent': '请确认授权当前登录信息给《斗罗宇宙》'
        },
      );
    }
    catch (e) {
      LogUtil.e('Exception launching game: $e');
      _showErrorDialog('启动游戏失败: $e');
    }
  }
  /// 显示错误对话框
  void _showErrorDialog(String message) {
    if (!mounted) return;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('错误'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}