import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'dart:math' as math;

enum ElementType { text, image, video }

enum NodeType { text, element }

class HtmlNode {
  final NodeType type;
  final String tagName;
  final Map<String, String> attributes;
  final String textContent;
  final List<HtmlNode> children;
  HtmlNode? parent;

  HtmlNode({
    required this.type,
    this.tagName = '',
    this.attributes = const {},
    this.textContent = '',
    List<HtmlNode>? children,
    this.parent,
  }) : children = children ?? [];

  Map<String, String> getInheritedStyles() {
    Map<String, String> styles = {};
    List<HtmlNode> ancestors = [];
    HtmlNode? current = this;

    // 收集所有祖先节点（从当前到根节点）
    while (current != null) {
      if (current.type == NodeType.element) {
        ancestors.add(current);
      }
      current = current.parent;
    }

    // 从根节点到当前节点应用样式，子节点样式覆盖父节点样式
    for (int i = ancestors.length - 1; i >= 0; i--) {
      HtmlNode node = ancestors[i];
      String styleAttr = node.attributes['style'] ?? '';
      if (styleAttr.isNotEmpty) {
        Map<String, String> nodeStyles = HtmlNode._parseStyleAttribute(
          styleAttr,
        );
        styles.addAll(nodeStyles); // 子节点样式覆盖父节点样式
      }

      // 处理独立的 color 属性
      if (node.attributes.containsKey('color')) {
        String colorValue = node.attributes['color']!;
        styles['color'] = colorValue;
      }
    }

    return styles;
  }

  static Map<String, String> _parseStyleAttribute(String styleAttr) {
    Map<String, String> styles = {};
    List<String> declarations = styleAttr.split(';');

    for (String declaration in declarations) {
      if (declaration.trim().isEmpty) continue;

      int colonIndex = declaration.indexOf(':');
      if (colonIndex > 0) {
        String property = declaration.substring(0, colonIndex).trim();
        String value = declaration.substring(colonIndex + 1).trim();
        styles[property] = value;
      }
    }

    return styles;
  }

  bool hasAncestorTag(String tagName) {
    HtmlNode? current = parent;
    while (current != null) {
      if (current.tagName.toLowerCase() == tagName.toLowerCase()) {
        return true;
      }
      current = current.parent;
    }
    return false;
  }

  @override
  String toString() {
    StringBuffer buffer = StringBuffer();
    buffer.write('HtmlNode(');
    buffer.write('type: $type, ');
    buffer.write('tagName: "$tagName", ');
    buffer.write('textContent: "${textContent.replaceAll('\n', '\\n')}", ');
    buffer.write('attributes: {');

    List<String> attributeEntries = [];
    attributes.forEach((key, value) {
      attributeEntries.add('"$key": "$value"');
    });
    buffer.write(attributeEntries.join(', '));

    buffer.write('}, ');
    buffer.write('children: ${children.length}, ');
    buffer.write('parent: ${parent?.tagName ?? 'null'}');
    buffer.write(')');

    return buffer.toString();
  }
}

class StyledText {
  final String text;
  final TextStyle textStyle;
  final Color? backgroundColor;
  final String? linkUrl; // 添加链接URL属性
  final bool isLink; // 标识是否为链接
  final String textId; // 添加文本唯一ID

  StyledText({
    required this.text,
    required this.textStyle,
    this.backgroundColor,
    this.linkUrl,
    this.isLink = false,
    String? textId,
  }) : textId = textId ?? _generateTextId();

  static String _generateTextId() {
    return 'text_${DateTime.now().microsecondsSinceEpoch}_${math.Random().nextInt(10000)}';
  }
}

class ContentElement {
  final ElementType type;
  final String content;
  final String? src;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final TextAlign? textAlign;
  final List<StyledText>? styledTexts; // 添加支持多样式文本
  final List<dynamic>? mixedInlineElements; // 添加支持混合内联元素（文本+图片）

  ContentElement({
    required this.type,
    this.content = '',
    this.src,
    this.textStyle,
    this.backgroundColor,
    this.textAlign,
    this.styledTexts,
    this.mixedInlineElements,
  });
}

class RichTextParser {
  static HtmlNode parseHtmlToTree(String html) {
    HtmlNode root = HtmlNode(type: NodeType.element, tagName: 'root');
    _parseHtmlContentToTree(html, root, '');
    return root;
  }

  static void _parseHtmlContentToTree(
    String html,
    HtmlNode parent,
    String sessionId,
  ) {
    int currentIndex = 0;

    while (currentIndex < html.length) {
      int tagStart = html.indexOf('<', currentIndex);

      if (tagStart == -1) {
        String remainingText = html.substring(currentIndex).trim();
        if (remainingText.isNotEmpty) {
          HtmlNode textNode = HtmlNode(
            type: NodeType.text,
            textContent: _cleanText(remainingText),
            parent: parent,
          );
          parent.children.add(textNode);
        }
        break;
      }

      if (tagStart > currentIndex) {
        String textContent = html.substring(currentIndex, tagStart).trim();
        if (textContent.isNotEmpty) {
          HtmlNode textNode = HtmlNode(
            type: NodeType.text,
            textContent: _cleanText(textContent),
            parent: parent,
          );
          parent.children.add(textNode);
        }
      }

      int tagEnd = html.indexOf('>', tagStart);
      if (tagEnd == -1) {
        break;
      }

      String tagContent = html.substring(tagStart + 1, tagEnd);

      if (tagContent.startsWith('/')) {
        currentIndex = tagEnd + 1;
        continue;
      }

      bool isSelfClosing =
          tagContent.endsWith('/') ||
          tagContent.startsWith('img') ||
          tagContent.startsWith('br') ||
          tagContent.startsWith('hr');

      Map<String, String> tagInfo = _parseTag(tagContent, sessionId);
      String tagName = tagInfo['tagName'] ?? '';

      Map<String, String> attributes = {};

      for (String key in tagInfo.keys) {
        if (key != 'tagName') {
          attributes[key] = tagInfo[key]!;
        }
      }

      if (isSelfClosing) {
        HtmlNode elementNode = HtmlNode(
          type: NodeType.element,
          tagName: tagName,
          attributes: attributes,
          parent: parent,
        );
        parent.children.add(elementNode);
        currentIndex = tagEnd + 1;
      } else {
        String endTag = '</$tagName>';

        int endTagIndex = _findMatchingEndTag(
          html,
          tagEnd + 1,
          tagName,
          sessionId,
        );

        if (endTagIndex == -1) {
          currentIndex = tagEnd + 1;
          continue;
        }

        HtmlNode elementNode = HtmlNode(
          type: NodeType.element,
          tagName: tagName,
          attributes: attributes,
          parent: parent,
          children: [],
        );
        parent.children.add(elementNode);

        String innerHtml = html.substring(tagEnd + 1, endTagIndex);
        _parseHtmlContentToTree(innerHtml, elementNode, sessionId);

        currentIndex = endTagIndex + endTag.length;
      }
    }
  }

  /// 找到匹配的结束标签，正确处理嵌套情况
  static int _findMatchingEndTag(
    String html,
    int startIndex,
    String tagName,
    String sessionId,
  ) {
    String startTag = '<$tagName';
    String endTag = '</$tagName>';

    int currentIndex = startIndex;
    int nestLevel = 1;

    while (currentIndex < html.length && nestLevel > 0) {
      int nextStartTag = html.indexOf(startTag, currentIndex);
      int nextEndTag = html.indexOf(endTag, currentIndex);

      if (nextEndTag == -1) {
        return -1;
      }

      if (nextStartTag != -1 && nextStartTag < nextEndTag) {
        int tagEndPos = html.indexOf('>', nextStartTag);
        if (tagEndPos != -1) {
          String fullTag = html.substring(nextStartTag, tagEndPos + 1);
          if (fullTag.startsWith('<$tagName ') || fullTag == '<$tagName>') {
            nestLevel++;
            currentIndex = tagEndPos + 1;
            continue;
          }
        }
        currentIndex = nextStartTag + 1;
        continue;
      }

      nestLevel--;
      if (nestLevel == 0) {
        return nextEndTag;
      }

      currentIndex = nextEndTag + endTag.length;
    }

    return -1;
  }

  static Map<String, String> _parseTag(String tagContent, String sessionId) {
    Map<String, String> result = <String, String>{};

    if (tagContent.endsWith('/')) {
      tagContent = tagContent.substring(0, tagContent.length - 1).trim();
    }

    int firstSpaceIndex = tagContent.indexOf(' ');

    if (firstSpaceIndex == -1) {
      result['tagName'] = tagContent.toLowerCase();
    } else {
      String tagName = tagContent.substring(0, firstSpaceIndex).toLowerCase();
      String attributeStr = tagContent.substring(firstSpaceIndex + 1).trim();

      result['tagName'] = tagName;

      if (attributeStr.isNotEmpty) {
        _parseAttributes(attributeStr, result, sessionId, '');
      }
    }
    return result;
  }

  static void _parseAttributes(
    String attributeStr,
    Map<String, String> result,
    String sessionId,
    String tagParseId,
  ) {
    int currentIndex = 0;

    while (currentIndex < attributeStr.length) {
      while (currentIndex < attributeStr.length &&
          attributeStr[currentIndex] == ' ') {
        currentIndex++;
      }

      if (currentIndex >= attributeStr.length) {
        break;
      }

      int equalIndex = attributeStr.indexOf('=', currentIndex);
      if (equalIndex == -1) {
        break;
      }

      String attrName = attributeStr.substring(currentIndex, equalIndex).trim();
      currentIndex = equalIndex + 1;

      while (currentIndex < attributeStr.length &&
          attributeStr[currentIndex] == ' ') {
        currentIndex++;
      }

      if (currentIndex >= attributeStr.length) {
        break;
      }

      String quote = attributeStr[currentIndex];

      if (quote == '"' || quote == "'") {
        currentIndex++;
        int endQuoteIndex = attributeStr.indexOf(quote, currentIndex);
        if (endQuoteIndex != -1) {
          String attrValue = attributeStr.substring(
            currentIndex,
            endQuoteIndex,
          );
          result[attrName] = attrValue;
          currentIndex = endQuoteIndex + 1;
        } else {
          break;
        }
      } else {
        int spaceIndex = attributeStr.indexOf(' ', currentIndex);
        if (spaceIndex == -1) spaceIndex = attributeStr.length;
        String attrValue = attributeStr.substring(currentIndex, spaceIndex);
        result[attrName] = attrValue;
        currentIndex = spaceIndex;
      }
    }
  }

  static List<ContentElement> parseHtmlContent(String html) {
    final List<ContentElement> elements = [];
    int currentIndex = 0;

    while (currentIndex < html.length) {
      int tagStart = html.indexOf('<', currentIndex);

      if (tagStart == -1) {
        final remainingText = html.substring(currentIndex).trim();
        if (remainingText.isNotEmpty) {
          elements.add(
            ContentElement(
              type: ElementType.text,
              content: _cleanText(remainingText),
            ),
          );
        }
        break;
      }

      if (tagStart > currentIndex) {
        final textContent = html.substring(currentIndex, tagStart).trim();
        if (textContent.isNotEmpty) {
          elements.add(
            ContentElement(
              type: ElementType.text,
              content: _cleanText(textContent),
            ),
          );
        }
      }

      int tagEnd = html.indexOf('>', tagStart);
      if (tagEnd == -1) break;

      String tagContent = html.substring(tagStart, tagEnd + 1);

      if (tagContent.startsWith('<img')) {
        elements.add(ContentElement(type: ElementType.image));
        currentIndex = tagEnd + 1;
      } else if (tagContent.startsWith('<video')) {
        String? videoSrc = extractVideoSrc(tagContent);
        if (videoSrc != null && videoSrc.isNotEmpty) {
          elements.add(ContentElement(type: ElementType.video, src: videoSrc));
        }
        currentIndex = tagEnd + 1;
      } else if (tagContent.startsWith('<p')) {
        int pEnd = html.indexOf('</p>', tagEnd + 1);
        if (pEnd != -1) {
          String pContent = html.substring(tagEnd + 1, pEnd);
          TextAlign? paragraphAlign = extractTextAlign(tagContent);
          final pElement = parseParagraph(
            pContent,
            paragraphAlign: paragraphAlign,
          );
          if (pElement != null) {
            elements.add(pElement);
          }
          currentIndex = pEnd + 4;
        } else {
          currentIndex = tagEnd + 1;
        }
      } else if (tagContent.startsWith('<h1') ||
          tagContent.startsWith('<h2') ||
          tagContent.startsWith('<h3') ||
          tagContent.startsWith('<h4') ||
          tagContent.startsWith('<h5') ||
          tagContent.startsWith('<h6')) {
        // 处理标题标签
        String tagName = tagContent.split(' ')[0].split('>')[0];
        if (tagName.startsWith('<')) tagName = tagName.substring(1);
        String endTag = '</$tagName>';
        int hEnd = html.indexOf(endTag, tagEnd + 1);
        if (hEnd != -1) {
          // 保持完整的H标签结构，而不是只提取内容
          String fullHContent = html.substring(tagStart, hEnd + endTag.length);
          final hElement = parseParagraph(fullHContent);
          if (hElement != null) {
            elements.add(hElement);
          }
          currentIndex = hEnd + endTag.length;
        } else {
          currentIndex = tagEnd + 1;
        }
      } else {
        currentIndex = tagEnd + 1;
      }
    }

    return elements;
  }

  static ContentElement? parseParagraph(
    String content, {
    TextAlign? paragraphAlign,
  }) {
    // 如果内容包含图片，我们需要创建一个特殊的混合内容元素
    // 这个元素将包含内联的文本和图片
    if (content.contains('<img')) {
      // 创建一个特殊的混合内容解析
      return _parseMixedParagraphContent(content, paragraphAlign);
    }

    if (content.contains('<video')) {
      String? videoSrc = extractVideoSrc(content);
      if (videoSrc != null && videoSrc.isNotEmpty) {
        return ContentElement(type: ElementType.video, src: videoSrc);
      } else {
        return null;
      }
    }

    HtmlNode root = parseHtmlToTree(content);
    List<StyledText> styledTexts = [];
    collectStyledTexts(root, styledTexts);

    if (styledTexts.isEmpty) {
      return null;
    }

    // 不要合并样式文本，保持原始的多样式信息
    if (styledTexts.length == 1) {
      StyledText styledText = styledTexts.first;
      return ContentElement(
        type: ElementType.text,
        content: styledText.text,
        textStyle: styledText.textStyle,
        backgroundColor: styledText.backgroundColor,
        textAlign: paragraphAlign,
      );
    } else {
      // 多样式文本，返回完整的styledTexts列表
      String combinedText = styledTexts.map((t) => t.text).join();
      return ContentElement(
        type: ElementType.text,
        content: combinedText,
        styledTexts: styledTexts,
        textAlign: paragraphAlign,
      );
    }
  }

  /// 解析包含图片的混合段落内容
  static ContentElement? _parseMixedParagraphContent(
    String content,
    TextAlign? paragraphAlign,
  ) {
    // 这里我们需要解析混合内容，但保持它们在同一个段落中
    // 我们将创建一个包含内联元素的特殊内容元素

    List<dynamic> inlineElements = []; // 包含文本片段和图片URL的列表
    int currentIndex = 0;

    while (currentIndex < content.length) {
      int imgStart = content.indexOf('<img', currentIndex);

      if (imgStart == -1) {
        // 没有更多图片，处理剩余文本
        String remainingText = content.substring(currentIndex);
        if (remainingText.trim().isNotEmpty) {
          // 解析剩余文本的样式
          HtmlNode textRoot = parseHtmlToTree(remainingText);
          List<StyledText> textStyles = [];
          collectStyledTexts(textRoot, textStyles);
          inlineElements.addAll(textStyles);
        }
        break;
      }

      // 处理图片前的文本
      if (imgStart > currentIndex) {
        String textContent = content.substring(currentIndex, imgStart);
        if (textContent.trim().isNotEmpty) {
          // 解析文本的样式
          HtmlNode textRoot = parseHtmlToTree(textContent);
          List<StyledText> textStyles = [];
          collectStyledTexts(textRoot, textStyles);
          inlineElements.addAll(textStyles);
        }
      }

      // 处理图片 - 改进图片结尾检测逻辑
      int imgEnd = -1;
      String imgTag = '';

      // 首先查找自闭合标签 />
      int selfClosingEnd = content.indexOf('/>', imgStart);
      // 然后查找普通结束 >
      int normalEnd = content.indexOf('>', imgStart);

      if (selfClosingEnd != -1 &&
          (normalEnd == -1 || selfClosingEnd < normalEnd)) {
        // 找到自闭合标签
        imgEnd = selfClosingEnd + 2;
        imgTag = content.substring(imgStart, imgEnd);
      } else if (normalEnd != -1) {
        // 找到普通结束标签
        imgEnd = normalEnd + 1;
        imgTag = content.substring(imgStart, imgEnd);
      }

      if (imgEnd != -1 && imgTag.isNotEmpty) {
        // 手动解析图片URL
        String? imageUrl = _extractImageUrlFromTag(imgTag);

        if (imageUrl != null && imageUrl.isNotEmpty) {
          inlineElements.add({'type': 'image', 'url': imageUrl});
        }
        currentIndex = imgEnd;
      } else {
        break;
      }
    }

    if (inlineElements.isEmpty) {
      return null;
    }

    // 创建一个特殊的混合内容元素
    return ContentElement(
      type: ElementType.text, // 我们标记为文本类型，但包含混合内联元素
      content: '', // 内容为空，因为实际内容在 mixedInlineElements 中
      textAlign: paragraphAlign,
      mixedInlineElements: inlineElements,
    );
  }

  /// 从图片标签中提取图片URL
  static String? _extractImageUrlFromTag(String imgTag) {
    int srcIndex = imgTag.indexOf('src=');
    if (srcIndex == -1) return null;

    int quoteStart = srcIndex + 4;
    if (quoteStart >= imgTag.length) return null;

    String quote = imgTag[quoteStart];
    if (quote == '\"' || quote == "'") {
      int quoteEnd = imgTag.indexOf(quote, quoteStart + 1);
      if (quoteEnd != -1) {
        return imgTag.substring(quoteStart + 1, quoteEnd);
      }
    } else {
      // 处理没有引号的情况
      int spaceIndex = imgTag.indexOf(' ', quoteStart);
      int endIndex = imgTag.indexOf('>', quoteStart);
      int endTagIndex = imgTag.indexOf('/>', quoteStart);

      int actualEnd = imgTag.length;
      if (spaceIndex != -1) actualEnd = spaceIndex;
      if (endIndex != -1 && endIndex < actualEnd) actualEnd = endIndex;
      if (endTagIndex != -1 && endTagIndex < actualEnd) actualEnd = endTagIndex;

      if (actualEnd > quoteStart) {
        return imgTag.substring(quoteStart, actualEnd);
      }
    }

    return null;
  }

  static void collectStyledTexts(HtmlNode node, List<StyledText> styledTexts) {
    if (node.type == NodeType.text) {
      String text = node.textContent.trim();

      if (text.isEmpty) {
        return;
      }

      Map<String, String> inheritedStyles = node.getInheritedStyles();

      bool isBold =
          node.hasAncestorTag('strong') ||
          node.hasAncestorTag('b') ||
          node.hasAncestorTag('h1') ||
          node.hasAncestorTag('h2') ||
          node.hasAncestorTag('h3') ||
          node.hasAncestorTag('h4') ||
          node.hasAncestorTag('h5') ||
          node.hasAncestorTag('h6');
      bool isItalic = node.hasAncestorTag('em') || node.hasAncestorTag('i');

      bool isStrikethrough =
          node.hasAncestorTag('del') ||
          node.hasAncestorTag('s') ||
          node.hasAncestorTag('strike');

      bool isUnderline = node.hasAncestorTag('u');

      bool isLink = node.hasAncestorTag('a');
      String? linkUrl;
      if (isLink) {
        linkUrl = _getAncestorLinkUrl(node);
        if (!inheritedStyles.containsKey('color')) {
          inheritedStyles = Map.from(inheritedStyles);
          inheritedStyles['color'] = '#1976d2';
        }
      }

      String? hTag = _getHTag(node);
      if (hTag != null) {
        inheritedStyles = Map.from(inheritedStyles);
        String fontSize = _getHTagFontSize(hTag);
        inheritedStyles['font-size'] = fontSize;
        isBold = true;
      }

      if (!inheritedStyles.containsKey('text-decoration')) {
        if (isStrikethrough) {
          inheritedStyles = Map.from(inheritedStyles);
          inheritedStyles['text-decoration'] = 'line-through';
        } else if (isUnderline) {
          inheritedStyles = Map.from(inheritedStyles);
          inheritedStyles['text-decoration'] = 'underline';
        }
      }

      TextStyle textStyle = buildTextStyleFromStyles(
        inheritedStyles,
        isBold,
        isItalic,
      );
      Color? backgroundColor = parseBackgroundColor(inheritedStyles);

      StyledText styledText = StyledText(
        text: text,
        textStyle: textStyle,
        backgroundColor: backgroundColor,
        linkUrl: linkUrl,
        isLink: isLink,
      );

      styledTexts.add(styledText);
    } else {
      for (int i = 0; i < node.children.length; i++) {
        HtmlNode child = node.children[i];
        collectStyledTexts(child, styledTexts);
      }
    }
  }

  static String? _getAncestorLinkUrl(HtmlNode node) {
    HtmlNode? current = node.parent;
    while (current != null) {
      if (current.tagName.toLowerCase() == 'a') {
        return current.attributes['href'];
      }
      current = current.parent;
    }
    return null;
  }

  static String? _getHTag(HtmlNode node) {
    HtmlNode? current = node.parent;
    while (current != null) {
      if (current.tagName.startsWith('h') && current.tagName.length == 2) {
        return current.tagName;
      }
      current = current.parent;
    }
    return null;
  }

  static String _getHTagFontSize(String hTag) {
    switch (hTag) {
      case 'h1':
        return '32px';
      case 'h2':
        return '24px';
      case 'h3':
        return '20px';
      case 'h4':
        return '18px';
      case 'h5':
        return '16px';
      case 'h6':
        return '14px';
      default:
        return '16px';
    }
  }

  static List<StyledText> mergeStyledTexts(List<StyledText> styledTexts) {
    if (styledTexts.isEmpty) return [];

    List<StyledText> merged = [];
    StyledText current = styledTexts.first;

    for (int i = 1; i < styledTexts.length; i++) {
      StyledText next = styledTexts[i];

      if (isSameStyle(current, next)) {
        current = StyledText(
          text: current.text + next.text,
          textStyle: current.textStyle,
          backgroundColor: current.backgroundColor,
        );
      } else {
        merged.add(current);
        current = next;
      }
    }

    merged.add(current);
    return merged;
  }

  static bool isSameStyle(StyledText a, StyledText b) {
    return a.textStyle.toString() == b.textStyle.toString() &&
        a.backgroundColor == b.backgroundColor;
  }

  static TextStyle buildTextStyleFromStyles(
    Map<String, String> styles,
    bool isBold,
    bool isItalic,
  ) {
    Color? textColor;
    TextDecoration? decoration;
    double fontSize = 16.0;

    if (styles.containsKey('color')) {
      textColor = parseColor(styles['color']!);
    }

    if (styles.containsKey('text-decoration')) {
      decoration = parseTextDecoration(styles['text-decoration']!);
    }

    if (styles.containsKey('font-size')) {
      fontSize = parseFontSize(styles['font-size']!);
    }

    return TextStyle(
      color: textColor ?? Colors.black87,
      fontSize: fontSize,
      decoration: decoration,
      decorationColor: textColor ?? Colors.black87,
      fontWeight: isBold ? FontWeight.bold : null,
      fontStyle: isItalic ? FontStyle.italic : null,
      height: 1.6,
    );
  }

  static Color? parseBackgroundColor(Map<String, String> styles) {
    if (styles.containsKey('background-color')) {
      return parseColor(styles['background-color']!);
    }
    return null;
  }

  static Color parseColor(String colorStr) {
    if (colorStr.startsWith('#')) {
      try {
        final hex = colorStr.replaceAll('#', '');
        if (hex.length == 6) {
          return Color(int.parse('FF$hex', radix: 16));
        }
      } catch (e) {
        // 解析失败
      }
    }

    switch (colorStr.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'blue':
        return Colors.blue;
      case 'green':
        return Colors.green;
      case 'yellow':
        return Colors.yellow;
      case 'orange':
        return Colors.orange;
      case 'purple':
        return Colors.purple;
      default:
        return Colors.black87;
    }
  }

  static TextAlign? extractTextAlign(String tagContent) {
    int styleIndex = tagContent.indexOf('style=');
    if (styleIndex == -1) return null;

    int quoteStart = styleIndex + 6;
    if (quoteStart >= tagContent.length) return null;

    String quote = tagContent[quoteStart];
    if (quote != '"' && quote != "'") return null;

    int quoteEnd = tagContent.indexOf(quote, quoteStart + 1);
    if (quoteEnd == -1) return null;

    String styleContent = tagContent.substring(quoteStart + 1, quoteEnd);

    int alignIndex = styleContent.indexOf('text-align:');
    if (alignIndex == -1) return null;

    int valueStart = alignIndex + 11;

    while (valueStart < styleContent.length &&
        styleContent[valueStart] == ' ') {
      valueStart++;
    }

    if (valueStart >= styleContent.length) return null;

    int valueEnd = styleContent.indexOf(';', valueStart);
    if (valueEnd == -1) valueEnd = styleContent.length;

    String alignValue =
        styleContent.substring(valueStart, valueEnd).trim().toLowerCase();

    switch (alignValue) {
      case 'center':
        return TextAlign.center;
      case 'left':
        return TextAlign.left;
      case 'right':
        return TextAlign.right;
      case 'justify':
        return TextAlign.justify;
      default:
        return null;
    }
  }

  static TextDecoration? parseTextDecoration(String decorationStr) {
    String normalized = decorationStr.toLowerCase().trim();
    switch (normalized) {
      case 'underline':
        return TextDecoration.underline;
      case 'line-through':
      case 'strikethrough':
      case 'strike':
        return TextDecoration.lineThrough;
      case 'overline':
        return TextDecoration.overline;
      case 'none':
        return TextDecoration.none;
      default:
        return null;
    }
  }

  static double parseFontSize(String fontSizeStr) {
    final trimmed = fontSizeStr.trim();
    try {
      if (trimmed.endsWith('px')) {
        final numStr = trimmed.substring(0, trimmed.length - 2);
        return double.parse(numStr);
      }
      if (trimmed.endsWith('pt')) {
        final numStr = trimmed.substring(0, trimmed.length - 2);
        return double.parse(numStr);
      }
      if (trimmed.endsWith('em')) {
        final numStr = trimmed.substring(0, trimmed.length - 2);
        return double.parse(numStr) * 16.0;
      }
      return double.parse(trimmed);
    } catch (e) {
      return 16.0;
    }
  }

  static String? extractVideoSrc(String content) {
    int srcIndex = content.indexOf('src=');
    if (srcIndex != -1) {
      int quoteStart = srcIndex + 4;
      if (quoteStart < content.length) {
        String quote = content[quoteStart];
        if (quote == '"' || quote == "'") {
          int quoteEnd = content.indexOf(quote, quoteStart + 1);
          if (quoteEnd != -1) {
            return content.substring(quoteStart + 1, quoteEnd);
          }
        }
      }
    }
    return null;
  }

  static String extractPlainText(String html) {
    return html
        .replaceAll(RegExp(r'<[^>]*>'), ' ')
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }

  static String _cleanText(String text) {
    String cleaned =
        text
            .replaceAll(RegExp(r'<[^>]*>'), '')
            .replaceAll('&nbsp;', ' ')
            .replaceAll('&amp;', '&')
            .replaceAll('&lt;', '<')
            .replaceAll('&gt;', '>')
            .replaceAll('&quot;', '"')
            .replaceAll('&#39;', "'")
            .trim();
    return cleaned;
  }

  static String? extractImageUrlFromHtml(String html, int imageIndex) {
    int currentImageIndex = 0;
    int searchStart = 0;

    while (searchStart < html.length) {
      int imgStart = html.indexOf('<img', searchStart);
      if (imgStart == -1) break;

      int imgEnd = html.indexOf('>', imgStart);
      if (imgEnd == -1) break;

      if (currentImageIndex == imageIndex) {
        String imgTag = html.substring(imgStart, imgEnd + 1);
        int srcIndex = imgTag.indexOf('src=');
        if (srcIndex != -1) {
          int quoteStart = srcIndex + 4;
          if (quoteStart < imgTag.length) {
            String quote = imgTag[quoteStart];
            if (quote == '"' || quote == "'") {
              int quoteEnd = imgTag.indexOf(quote, quoteStart + 1);
              if (quoteEnd != -1) {
                return imgTag.substring(quoteStart + 1, quoteEnd);
              }
            }
          }
        }
        break;
      }

      currentImageIndex++;
      searchStart = imgEnd + 1;
    }

    return null;
  }

  static String? extractImageUrl(dynamic imageData) {
    if (imageData is Map<String, dynamic>) {
      return imageData['url'] ??
          imageData['thumbUrl'] ??
          imageData['attachment'];
    } else if (imageData is String) {
      return imageData;
    }
    return null;
  }

  static String extractTextFromHtml(String html) {
    if (html.isEmpty) return '';

    String result = html;

    result = result
        .replaceAll(RegExp(r'<div[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</div>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<p[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</p>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<h[1-6][^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</h[1-6]>', caseSensitive: false), '\n');

    result = result
        .replaceAll(RegExp(r'<br[^>]*/?>', caseSensitive: false), '\n')
        .replaceAll(
          RegExp(r'<hr[^>]*/?>', caseSensitive: false),
          '\n——————————\n',
        );

    result = result
        .replaceAll(RegExp(r'<ul[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</ul>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<ol[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</ol>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<li[^>]*>', caseSensitive: false), '\n• ')
        .replaceAll(RegExp(r'</li>', caseSensitive: false), '');

    result = result
        .replaceAll(RegExp(r'<table[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</table>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'<tr[^>]*>', caseSensitive: false), '\n')
        .replaceAll(RegExp(r'</tr>', caseSensitive: false), '')
        .replaceAll(RegExp(r'<td[^>]*>', caseSensitive: false), ' ')
        .replaceAll(RegExp(r'</td>', caseSensitive: false), ' | ')
        .replaceAll(RegExp(r'<th[^>]*>', caseSensitive: false), ' ')
        .replaceAll(RegExp(r'</th>', caseSensitive: false), ' | ');

    result = result
        .replaceAll(RegExp(r'<strong[^>]*>', caseSensitive: false), '**')
        .replaceAll(RegExp(r'</strong>', caseSensitive: false), '**')
        .replaceAll(RegExp(r'<b[^>]*>', caseSensitive: false), '**')
        .replaceAll(RegExp(r'</b>', caseSensitive: false), '**')
        .replaceAll(RegExp(r'<em[^>]*>', caseSensitive: false), '*')
        .replaceAll(RegExp(r'</em>', caseSensitive: false), '*')
        .replaceAll(RegExp(r'<i[^>]*>', caseSensitive: false), '*')
        .replaceAll(RegExp(r'</i>', caseSensitive: false), '*')
        .replaceAll(RegExp(r'<u[^>]*>', caseSensitive: false), '_')
        .replaceAll(RegExp(r'</u>', caseSensitive: false), '_');

    result = result.replaceAllMapped(
      RegExp(
        r'<a[^>]*href=["\'
        ']([^"\']*)["\''
        '][^>]*>(.*?)</a>',
        caseSensitive: false,
      ),
      (match) {
        String linkText = match.group(2) ?? '';
        String url = match.group(1) ?? '';
        if (linkText.isNotEmpty && url.isNotEmpty && linkText != url) {
          return '$linkText ($url)';
        } else if (linkText.isNotEmpty) {
          return linkText;
        } else {
          return url;
        }
      },
    );

    result = result
        .replaceAll(RegExp(r'<blockquote[^>]*>', caseSensitive: false), '\n> ')
        .replaceAll(RegExp(r'</blockquote>', caseSensitive: false), '\n');

    result = result
        .replaceAll(RegExp(r'<pre[^>]*>', caseSensitive: false), '\n```\n')
        .replaceAll(RegExp(r'</pre>', caseSensitive: false), '\n```\n')
        .replaceAll(RegExp(r'<code[^>]*>', caseSensitive: false), '`')
        .replaceAll(RegExp(r'</code>', caseSensitive: false), '`');

    result = result.replaceAll(RegExp(r'<[^>]*>'), '');

    result = result
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&#34;', '"')
        .replaceAll('&ldquo;', '"')
        .replaceAll('&rdquo;', '"')
        .replaceAll('&lsquo;', "'")
        .replaceAll('&rsquo;', "'")
        .replaceAll('&mdash;', '—')
        .replaceAll('&ndash;', '–')
        .replaceAll('&hellip;', '…')
        .replaceAll('&copy;', '©')
        .replaceAll('&reg;', '®')
        .replaceAll('&trade;', '™');

    result =
        result
            .replaceAll(RegExp(r' +'), ' ')
            .replaceAll(RegExp(r'\n\s*\n\s*\n+'), '\n\n')
            .replaceAll(RegExp(r'\n +'), '\n')
            .replaceAll(RegExp(r' +\n'), '\n')
            .trim();

    return result;
  }

  /// 构建多样式文本的TextSpan列表
  static List<TextSpan> buildTextSpans(
    List<StyledText> styledTexts, {
    Function(String, String)? onLinkTap,
  }) {
    return styledTexts.map((styledText) {
      if (styledText.isLink &&
          styledText.linkUrl != null &&
          onLinkTap != null) {
        return TextSpan(
          text: styledText.text,
          style: styledText.textStyle,
          recognizer:
              TapGestureRecognizer()
                ..onTap = () {
                  onLinkTap(styledText.linkUrl!, styledText.text);
                },
        );
      } else {
        return TextSpan(text: styledText.text, style: styledText.textStyle);
      }
    }).toList();
  }

  /// 构建RichText Widget
  static Widget buildRichTextWidget(
    ContentElement element, {
    Function(String, String)? onLinkTap,
  }) {
    if (element.styledTexts != null && element.styledTexts!.length > 1) {
      return RichText(
        textAlign: element.textAlign ?? TextAlign.left,
        text: TextSpan(
          children: buildTextSpans(element.styledTexts!, onLinkTap: onLinkTap),
        ),
      );
    } else {
      // 单一样式或使用普通Text Widget
      return Text(
        element.content,
        style: element.textStyle,
        textAlign: element.textAlign,
      );
    }
  }
}
