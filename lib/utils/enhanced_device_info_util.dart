import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/material.dart';

/// 增强的设备信息工具类
class EnhancedDeviceInfoUtil {
  static DeviceInfoPlugin? _deviceInfoPlugin;
  static PackageInfo? _packageInfo;
  static AndroidDeviceInfo? _androidInfo;
  static IosDeviceInfo? _iosInfo;
  
  /// 初始化设备信息
  static Future<void> initialize() async {
    _deviceInfoPlugin ??= DeviceInfoPlugin();
    _packageInfo ??= await PackageInfo.fromPlatform();
    
    if (Platform.isAndroid) {
      _androidInfo ??= await _deviceInfoPlugin!.androidInfo;
    } else if (Platform.isIOS) {
      _iosInfo ??= await _deviceInfoPlugin!.iosInfo;
    }
  }

  /// 获取屏幕宽度像素 (需要context)
  static int getWidthPixels(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    return (size.width * devicePixelRatio).round();
  }

  /// 获取屏幕高度像素 (需要context)
  static int getHeightPixels(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    return (size.height * devicePixelRatio).round();
  }

  /// 获取设备型号
  static Future<String> getDeviceModel() async {
    await initialize();
    if (Platform.isAndroid) {
      return _androidInfo?.model ?? 'Unknown';
    } else if (Platform.isIOS) {
      return _iosInfo?.model ?? 'Unknown';
    }
    return 'Unknown';
  }

  /// 获取操作系统版本描述
  static Future<String> getOSDescription() async {
    await initialize();
    if (Platform.isAndroid) {
      return 'Android ${_androidInfo?.version.release ?? 'Unknown'}';
    } else if (Platform.isIOS) {
      return 'iOS ${_iosInfo?.systemVersion ?? 'Unknown'}';
    }
    return Platform.operatingSystemVersion;
  }

  /// 获取操作系统版本号
  static Future<String> getOSVersion() async {
    await initialize();
    if (Platform.isAndroid) {
      return _androidInfo?.version.sdkInt.toString() ?? '0';
    } else if (Platform.isIOS) {
      return _iosInfo?.systemVersion ?? '0';
    }
    return '0';
  }

  /// 获取设备品牌
  static Future<String> getDeviceBrand() async {
    await initialize();
    if (Platform.isAndroid) {
      return _androidInfo?.brand ?? 'Unknown';
    } else if (Platform.isIOS) {
      return 'Apple';
    }
    return 'Unknown';
  }

  /// 获取应用包名
  static Future<String> getPackageName() async {
    await initialize();
    return _packageInfo?.packageName ?? '';
  }

  /// 获取版本号
  static Future<String> getVersionCode() async {
    await initialize();
    return _packageInfo?.buildNumber ?? '1';
  }

  /// 获取版本名称
  static Future<String> getVersionName() async {
    await initialize();
    return _packageInfo?.version ?? '1.0.0';
  }

  /// 获取应用名称
  static Future<String> getAppName() async {
    await initialize();
    return _packageInfo?.appName ?? '';
  }

  /// 检查是否为平板
  static Future<bool> isTablet() async {
    await initialize();
    if (Platform.isAndroid) {
      // Android判断逻辑可以根据屏幕尺寸等因素
      return false; // 简化版本
    } else if (Platform.isIOS) {
      return _iosInfo?.model.toLowerCase().contains('ipad') ?? false;
    }
    return false;
  }

  /// 检查是否为模拟器
  static Future<bool> isEmulator() async {
    await initialize();
    if (Platform.isAndroid) {
      return !(_androidInfo?.isPhysicalDevice ?? true);
    } else if (Platform.isIOS) {
      return !(_iosInfo?.isPhysicalDevice ?? true);
    }
    return false;
  }


  /// 获取设备标识符 (跨平台)
  static Future<String> getDeviceIdentifier() async {
    await initialize();
    if (Platform.isAndroid) {
      return _androidInfo?.id ?? '';
    } else if (Platform.isIOS) {
      return _iosInfo?.identifierForVendor ?? '';
    }
    return '';
  }
}