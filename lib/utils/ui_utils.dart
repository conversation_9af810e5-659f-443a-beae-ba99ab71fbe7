import 'package:flutter/material.dart';

/// UI工具类
class UIUtils {
  /// 获取底部安全区域的高度
  /// 如果系统设置了三键导航栏，会返回导航栏高度
  /// 否则返回0
  static double getBottomSafeAreaHeight(BuildContext context) {
    return MediaQuery.of(context).viewPadding.bottom;
  }

  /// 获取底部安全区域的内边距
  /// 如果系统设置了三键导航栏，会返回导航栏高度 + 额外间距
  /// 否则返回默认间距
  static EdgeInsets getBottomPadding(
    BuildContext context, {
    double horizontalPadding = 16.0,
    double defaultBottomPadding = 40.0,
    double extraPadding = 20.0,
  }) {
    final bottomPadding = getBottomSafeAreaHeight(context);
    return EdgeInsets.fromLTRB(
      horizontalPadding,
      0,
      horizontalPadding,
      bottomPadding > 0 ? bottomPadding + extraPadding : defaultBottomPadding,
    );
  }

  /// 获取底部安全区域的间距高度
  /// 如果系统设置了三键导航栏，会返回导航栏高度 + 额外间距
  /// 否则返回默认间距
  static double getBottomSpacing(
    BuildContext context, {
    double defaultSpacing = 40.0,
    double extraSpacing = 20.0,
  }) {
    final bottomPadding = getBottomSafeAreaHeight(context);
    return bottomPadding > 0 ? bottomPadding + extraSpacing : defaultSpacing;
  }

  /// 检查是否为三键导航栏模式
  static bool isThreeButtonNavigation(BuildContext context) {
    return getBottomSafeAreaHeight(context) > 0;
  }
}
