import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/permission_utils.dart';

/// 权限容器页面 - 透明页面用于处理权限设置跳转
class PermissionContainerPage extends StatefulWidget {
  final PermissionType permissionType;
  final String? title;
  final String? description;

  const PermissionContainerPage({
    Key? key,
    required this.permissionType,
    this.title,
    this.description,
  }) : super(key: key);

  /// 静态方法：跳转到权限容器页面并等待结果
  static Future<bool> requestPermissionWithSettings(
    BuildContext context,
    PermissionType permissionType, {
    String? title,
    String? description,
  }) async {
    final result = await Navigator.of(context).push<bool>(
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) => PermissionContainerPage(
              permissionType: permissionType,
              description: description,
            ),
        transitionDuration: Duration.zero,
        reverseTransitionDuration: Duration.zero,
        opaque: false,
      ),
    );
    return result ?? false;
  }

  @override
  State<PermissionContainerPage> createState() =>
      _PermissionContainerPageState();
}

class _PermissionContainerPageState extends State<PermissionContainerPage>
    with WidgetsBindingObserver {
  bool _hasNavigatedToSettings = false;
  bool _isCheckingPermission = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // 延迟一下再跳转到设置页面，确保页面已经完全加载
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _navigateToSettings();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用从后台回到前台时，检查权限状态
    if (state == AppLifecycleState.resumed &&
        _hasNavigatedToSettings &&
        !_isCheckingPermission) {
      _checkPermissionAndReturn();
    }
  }

  /// 跳转到系统设置页面
  void _navigateToSettings() async {
    if (_hasNavigatedToSettings) return;

    _hasNavigatedToSettings = true;

    // 显示提示对话框
    final shouldNavigate = await _showNavigateDialog();
    if (shouldNavigate) {
      // 跳转到系统设置
      await openAppSettings();
    } else {
      // 用户取消，直接返回 false
      if (mounted) {
        Navigator.of(context).pop(false);
      }
    }
  }

  /// 显示跳转提示对话框
  Future<bool> _showNavigateDialog() async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                  child: Text(
                    widget.title ?? '',
                    style: const TextStyle(
                      fontSize: 17,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // 内容
                Padding(
                  padding: const EdgeInsets.fromLTRB(15, 0, 15, 20),
                  child: Column(
                    children: [
                      Text(
                        widget.description ?? "",
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        '权限已被拒绝，请在系统设置中手动开启权限。',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.red,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                // 底部按钮
                Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                    ),
                  ),
                  child: Row(
                    children: [
                      // 取消按钮
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pop(false);
                          },
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.zero,
                            ),
                          ),
                          child: const Text(
                            '取消',
                            style: TextStyle(
                              color: Colors.black87,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),

                      // 中间分割线
                      Container(
                        width: 1,
                        height: 48,
                        color: const Color(0xFFEEEEEE),
                      ),

                      // 去设置按钮
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pop(true);
                            openAppSettings();
                          },
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.zero,
                            ),
                          ),
                          child: const Text(
                            '去设置',
                            style: TextStyle(
                              color: Colors.blue,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    ) ?? false;
  }

  /// 检查权限状态并返回结果
  void _checkPermissionAndReturn() async {
    if (_isCheckingPermission) return;

    _isCheckingPermission = true;

    // 延迟一下再检查，确保用户已经完成设置操作
    await Future.delayed(const Duration(milliseconds: 200));

    try {
      final isGranted = await PermissionUtils.isPermissionGranted(
        widget.permissionType,
      );

      if (mounted) {
        Navigator.of(context).pop(isGranted);
      }
    } catch (e) {
      // 检查权限出错，返回 false
      if (mounted) {
        Navigator.of(context).pop(false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 透明页面
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [],
        ),
      ),
    );
  }
}
