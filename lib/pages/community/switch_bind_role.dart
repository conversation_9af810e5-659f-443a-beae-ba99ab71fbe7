import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../components/game_character_binding_dialog.dart';
import '../../model/game_role.dart';
import '../../providers/role_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/log_util.dart';
import '../../track/circle_track.dart';
class SwitchBindRoleDialog {
  /// 显示角色切换弹窗
  static void showCharacterSwitchDialog(BuildContext context, {
    Function? onCharacterSwitched,
    VoidCallback? onNavigateToBindCharacter,
    VoidCallback? onClose,
    bool isNewGift = false,
    bool isCurrentAccount = false
  }) {
    final roleProvider = Provider.of<RoleProvider>(context, listen: false);
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final isLoggedIn = userProvider.isLoggedIn;
    final user = userProvider.currentUser;
    
    // 确定头像URL - 优先使用用户头像，否则使用默认头像
    final avatarUrl = isLoggedIn &&
                     user?.userDetail.avatar != null &&
                     user!.userDetail.avatar!.isNotEmpty
        ? user.userDetail.avatar!
        : 'assets/images/avatar.png';
    
    // 转换角色数据格式
    final characters = roleProvider.boundRoles
        .map(
          (role) => GameCharacter(
            id: role.roleFavoriteId.toString(),
            name: role.drname,
            server: role.dsname,
            level: role.drlevel,
            // 角色等级信息在V2接口中没有，暂时为空
            avatar: avatarUrl,
            // 使用用户头像或默认头像
            isSelected: role.isPicked,
            isCurrent: role.isCurrent,
          ),
        )
        .toList();

    GameCharacterBindingDialog.show(
      context: context,
      characters: characters,
      isNewGift: isNewGift,
      isCurrentAccount: isCurrentAccount,
      onCharacterSelected: (selectedCharacter) {
        _handleCharacterSwitch(context, selectedCharacter, onCharacterSwitched, onClose);
      },
      onBindOtherCharacter: () {
        onNavigateToBindCharacter?.call();
      },
      onClose: () {
        // 弹窗关闭时不需要特殊处理
        onClose?.call();
      },
    );
  }

  /// 处理角色切换
  static Future<void> _handleCharacterSwitch(
    BuildContext context, 
    GameCharacter selectedCharacter,
    Function? onCharacterSwitched,
    VoidCallback? onClose,
  ) async {
    try {
      final roleProvider = Provider.of<RoleProvider>(context, listen: false);
      
      // 找到对应的角色数据
      final selectedRoleData = roleProvider.boundRoles.firstWhere(
        (role) => role.roleFavoriteId.toString() == selectedCharacter.id,
      );

      LogUtil.d('开始切换角色: ${selectedCharacter.name}');

      // 调用切换角色接口
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final uid = userProvider.currentUser?.muid ?? '';

      final success = await roleProvider.setDefaultRole(
        context,
        roleFavoriteId: selectedRoleData.roleFavoriteId.toString(),
        uid: uid,
        rolePid: selectedRoleData.rolePid.toString(),
        roleGid: selectedRoleData.roleGid.toString(),
        drid: selectedRoleData.drid.toString(),
        dsid: selectedRoleData.dsid,
        drname: selectedRoleData.drname,
        dsname: selectedRoleData.dsname,
      );

      if (success) {
        LogUtil.d('角色切换成功');
        // 关闭弹窗
        Navigator.of(context).pop();
        if (context.mounted) {
          // 调用回调函数通知父页面刷新
          onCharacterSwitched?.call();
          onClose?.call();
        }
      } else {
        // 关闭弹窗
        if (context.mounted) {
          Fluttertoast.showToast(
            msg: roleProvider.error ?? '角色切换失败',
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
          );
        }
      }
    } catch (e) {
      LogUtil.e('角色切换异常: $e');
      if (context.mounted) {
        Fluttertoast.showToast(
          msg: '角色切换失败: $e',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
        );
      }
    }
  }

  static GameRoleV2? getSelectedRole(BuildContext context) {
    final roleProvider = Provider.of<RoleProvider>(context, listen: false);
    GameRoleV2? selectRole;
    try {
      selectRole = roleProvider.boundRoles.firstWhere((role) => role.isPicked);
    } catch (e) {
      LogUtil.e('getSelectedRole error: $e');
    }
    return selectRole;
  }

  static List<GameRoleV2> getBindingRoles(BuildContext context) {
    final roleProvider = Provider.of<RoleProvider>(context, listen: false);
    return roleProvider.boundRoles;
  }
}