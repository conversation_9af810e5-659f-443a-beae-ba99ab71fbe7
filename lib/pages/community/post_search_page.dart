import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../common/dl_color.dart';
import '../../components/refresh_header.dart';
import '../../components/forum_post_item.dart';
import '../../model/forum_post_list.dart';
import '../../net/config/http_base_config.dart';
import '../../net/api/forum_service.dart';
import '../../track/track.dart';
import '../../track/forum_track.dart';
import 'community_detail_page.dart';
import 'user_profile_page.dart';
import 'topic_page.dart';

/// 帖子搜索页（UI 实现，数据接入可后续扩展）
class PostSearchPage extends StatefulWidget {
  final String initialKeyword;

  const PostSearchPage({super.key, this.initialKeyword = ''});

  @override
  State<PostSearchPage> createState() => _PostSearchPageState();
}

class _PostSearchPageState extends State<PostSearchPage> with SingleTickerProviderStateMixin {
  late final TextEditingController _searchController;
  late final TabController _tabController;
  List<ForumPost> _searchResults = [];
  int _totalCount = 0;
  bool _isSearching = false;
  bool _hasSearched = false;
  // 预缓存的指示器图片，参考 CommunityPage 实现
  final AssetImage _tabIndicatorAsset = const AssetImage('assets/images/tab_indicator.png');
  // 分页与滚动
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  int _totalPage = 1;
  bool _hasMore = true;
  bool _isLoadingMore = false;
  String _lastKeyword = '';
  
  // 关注状态管理
  final Set<int> _followingUserIds = <int>{};
  final Set<int> _followLoadingUserIds = <int>{};

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialKeyword);
    _tabController = TabController(length: 1, vsync: this);
    _scrollController.addListener(_handleScroll);
    
    // 搜索页埋点
    ForumTrack.trackSearchViewShow();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 预加载Tab指示器图片，保证首次展示时即可绘制
    precacheImage(_tabIndicatorAsset, context);
  }

  Future<void> _onSearch() async {
    FocusScope.of(context).unfocus();
    final keyword = _searchController.text.trim();
    if (keyword.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
        _hasSearched = false;
      });
      Fluttertoast.showToast(msg: '请输入需要搜索的内容');
      return;
    }
    
    setState(() {
      _isSearching = true;
      _hasSearched = false;
      _searchResults.clear();
      _currentPage = 1;
      _hasMore = true;
      _lastKeyword = keyword;
    });

    ForumTrack.trackSearchClick(keyword: keyword);
    _searchPosts(keyword, page: 1, append: false);
  }

  void _handleScroll() {
    if (!_hasSearched || !_hasMore || _isLoadingMore || _isSearching) return;
    if (!_scrollController.hasClients) return;
    final position = _scrollController.position;
    if (position.extentAfter < 300) {
      setState(() {
        _isLoadingMore = true;
      });
      _searchPosts(_lastKeyword, page: _currentPage + 1, append: true);
    }
  }

  Future<void> _searchPosts(String keyword, {int page = 1, bool append = false}) async {
    try {
      final forumService = ForumService();
      final resp = await forumService.getPostList(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        page: page,
        pageSize: 20,
        scope: 2,
        filterSearch: keyword,
        context: context
      );
      if (!mounted) return;
      setState(() {
        _isSearching = false;
        _hasSearched = true;
        if (resp.isSuccess && resp.data != null) {
          final data = resp.data;
          if (data != null) {
            if (append) {
            _searchResults.addAll(data.pageData);
            // 加载更多时添加新的关注状态
            for (final post in data.pageData) {
              if (post.user.follow == 1 || post.user.follow == 2) {
                _followingUserIds.add(post.user.userId);
              }
            }
          } else {
            _searchResults = data.pageData;
            // 重置时初始化关注状态
            _followingUserIds.clear();
            for (final post in data.pageData) {
              if (post.user.follow == 1 || post.user.follow == 2) {
                _followingUserIds.add(post.user.userId);
              }
            }
          }
          _currentPage = data.currentPage;
          _totalPage = data.totalPage;
          _totalCount = data.totalCount;
          _hasMore = _currentPage < _totalPage && data.pageData.isNotEmpty;
          }
        } else {
          if (!append) {
            _searchResults = [];
            _followingUserIds.clear();
          }
          _totalCount = 0;
          _hasMore = false;
        }
        _isLoadingMore = false;
      });
      if (resp.isSuccess) {
        Fluttertoast.showToast(msg: '查询成功');
      }else{
        Fluttertoast.showToast(msg: resp.message);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isSearching = false;
        _hasSearched = true;
        if (!append) {
          _searchResults = [];
        }
        _isLoadingMore = false;
      });
      Fluttertoast.showToast(msg: '网络错误: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            _buildSearchBar(context),
            _buildTabs(),
            const Divider(height: 1, thickness: 1, color: DLColor.divider),
            Expanded(child: _buildSearchContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, size: 18, color: Colors.black87),
            onPressed: () => Navigator.of(context).pop(),
          ),
          Expanded(
            child: Container(
              height: 36,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F6F8),
                borderRadius: BorderRadius.circular(18),
              ),
              child: Row(
                children: [
                  const Icon(Icons.search, size: 18, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      textInputAction: TextInputAction.search,
                      decoration: const InputDecoration(
                        hintText: '请输入想要搜索的内容',
                        isDense: true,
                        border: InputBorder.none,
                      ),
                      onSubmitted: (_) => _onSearch(),
                    ),
                  ),
                  if (_searchController.text.isNotEmpty)
                    GestureDetector(
                      onTap: () {
                        _searchController.clear();
                        setState(() {});
                      },
                      child: const Icon(Icons.close, size: 18, color: Colors.grey),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: _onSearch,
            child: Container(
              height: 34,
              padding: const EdgeInsets.symmetric(horizontal: 14),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: const Color(0xFF4571FB),
                borderRadius: BorderRadius.circular(17),
              ),
              child: const Text('搜索', style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabs() {
    return Transform.translate(
      offset: const Offset(-22, 0),
      child: SizedBox(
        height: 40,
        child: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelPadding: const EdgeInsets.only(right: 16),
          padding: const EdgeInsets.only(left: 0),
          labelColor: Colors.black,
          unselectedLabelColor: Colors.black54,
          labelStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          unselectedLabelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
          // 使用与 CommunityPage 相同的自定义图片指示器
          indicator: ImageTabIndicator(
            image: _tabIndicatorAsset,
            imageSize: const Size(24, 8),
            bottomPadding: 4,
          ),
          dividerColor: Colors.transparent,
          splashFactory: NoSplash.splashFactory,
          overlayColor: MaterialStateProperty.all(Colors.transparent),
          tabs: const [
            Tab(text: '帖子'),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchContent() {
    if (_isSearching) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (!_hasSearched) {
      return _buildEmptyState();
    }
    
    if (_searchResults.isEmpty) {
      return _buildNoResultsState();
    }
    
    return _buildSearchResults();
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFFF2F4F7),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(Icons.search, size: 60, color: Colors.grey[400]),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            '搜索你感兴趣的内容',
            style: TextStyle(
              color: Color(0xFF8C8C8C),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: const Color(0xFFF2F4F7),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(Icons.search_off, size: 60, color: Colors.grey[400]),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            '暂无相关搜索结果',
            style: TextStyle(
              color: Color(0xFF8C8C8C),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '换个关键词试试吧',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSearchResults() {

    EasyRefreshController controller = EasyRefreshController(
        controlFinishRefresh: true,
        controlFinishLoad: true
    );
    return EasyRefresh(
        controller: controller,
        header: const CustomerHeader(),
        onRefresh: () async {
          if (_lastKeyword.isEmpty) return;
          setState(() {
            _currentPage = 1;
            _hasMore = true;
            _isSearching = true;
          });
          await _searchPosts(_lastKeyword, page: 1, append: false);
          controller.finishRefresh();
          controller.resetFooter();
        },
        child: ListView.builder(
          controller: _scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.only(bottom: 16,top: 16),
          itemCount: (_searchResults.isEmpty ? 0 : 1) + _searchResults.length + 1,
          itemBuilder: (context, index) {
            // 顶部统计行
            if (_searchResults.isNotEmpty && index == 0) {
              // 与上方 Tab 的左边对齐：Tab 有一个 -22 的左移；列表有 16 的左内边距
              // 因此这里整体左移 38 像素（16 + 22）
              return Transform.translate(
                offset: const Offset(14, 0),
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 12,left: 16),
                  child: Text(
                    '搜索结果（$_totalCount）',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: DLColor.textFifth,
                    ),
                  ),
                ),
              );
            }

            final listIndex = _searchResults.isNotEmpty ? index - 1 : index;

            if (listIndex == _searchResults.length) {
              if (_isLoadingMore) {
                return const Padding(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  child: Center(
                    child: SizedBox(
                      height: 24,
                      width: 24,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                );
              }
              if (!_hasMore) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: Center(
                    child: Text(
                      '没有更多了',
                      style: TextStyle(color: Colors.grey[500], fontSize: 12),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }
            final post = _searchResults[listIndex];
            return _buildPostItem(post, isFirstItem: listIndex == 0);
          },
        )
    );
  }
  
  
  

  /// 构建帖子列表项
  Widget _buildPostItem(ForumPost post, {bool isFirstItem = false}) {
    return ForumPostItem(
      post: post,
      index: 0, // 搜索页不需要特殊的索引处理
      isFirstItem: isFirstItem,
      showVoteWidget: false, // 搜索页不显示投票组件
      followingUserIds: _followingUserIds,
      followLoadingUserIds: _followLoadingUserIds,
      onTap: () => _navigateToPostDetail(post),
      onUserTap: (user) => _navigateToUserProfile(user),
      onFollowTap: (user) => _onFollowTap(user),
      onTopicTap: (topicId) => _navigateToTopicPage(topicId),
      onImageGalleryTap: (context, images, index, {post}) => _showImageGallery(context, images, index),
        contentPadding: EdgeInsets.only(left: 16,right: 16)
    );
  }

  
  
  

  



  void _navigateToTopicPage(String topicIdStr) {
    final int? topicId = int.tryParse(topicIdStr);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TopicPage(
          topicName: topicIdStr,
          topicId: topicId,
          description: null,
          avatarUrl: null,
          viewCount: 0,
          likeCount: 0,
          threadCount: 0,
        ),
      ),
    );
  }
  
  
  
  
  
  /// 跳转到帖子详情页
  void _navigateToPostDetail(ForumPost post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommunityDetailPage(post: post),
      ),
    );
  }
  
  /// 跳转到用户个人资料页面
  void _navigateToUserProfile(ForumUser user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfilePage(user: user),
      ),
    );
  }
  
  /// 显示图片画廊（支持左右滑动）
  void _showImageGallery(BuildContext context, List<dynamic> images, int initialIndex) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        return _ImageGalleryDialog(images: images, initialIndex: initialIndex);
      },
    );
  }





  /// 关注用户
  Future<void> _onFollowTap(ForumUser user) async {
    ForumTrack.trackFollowClick();
    // 如果正在关注中，直接返回
    if (_followLoadingUserIds.contains(user.userId)) {
      return;
    }
    
    // 如果已经关注，直接返回
    if (_followingUserIds.contains(user.userId)) {
      return;
    }
    
    // 添加到关注中状态
    setState(() {
      _followLoadingUserIds.add(user.userId);
    });
    
    try {
      final response = await ForumService().followUser(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        toUserId: user.userId,
        context: context,
      );
      
      if (!mounted) return;
      
      if (response.isSuccess) {
        // 关注成功，添加到已关注列表
        setState(() {
          _followingUserIds.add(user.userId);
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已关注 ${user.nickname}'),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        // 关注失败
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message ?? '关注失败'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('关注失败: $e'),
          duration: const Duration(seconds: 2),
        ),
      );
    } finally {
      // 移除关注中状态
      if (mounted) {
        setState(() {
          _followLoadingUserIds.remove(user.userId);
        });
      }
    }
  }
}

/// 一个小箭头形状的指示器，绘制在选中 Tab 的下方居中
class _SmallArrowIndicator extends Decoration {
  const _SmallArrowIndicator();

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _SmallArrowPainter();
  }
}

class _SmallArrowPainter extends BoxPainter {
  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Rect rect = offset & configuration.size!;
    final double arrowWidth = 18;
    final double arrowHeight = 6;
    final double centerX = rect.left + rect.width / 2;
    final double bottomY = rect.bottom;

    final Path path = Path()
      ..moveTo(centerX - arrowWidth / 2, bottomY)
      ..lineTo(centerX, bottomY - arrowHeight)
      ..lineTo(centerX + arrowWidth / 2, bottomY)
      ..close();

    final Paint paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, paint);
  }
}

/// 图片画廊弹窗组件
class _ImageGalleryDialog extends StatefulWidget {
  final List<dynamic> images;
  final int initialIndex;

  const _ImageGalleryDialog({
    required this.images,
    required this.initialIndex,
  });

  @override
  State<_ImageGalleryDialog> createState() => _ImageGalleryDialogState();
}

class _ImageGalleryDialogState extends State<_ImageGalleryDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  String? _getImageUrl(dynamic imageData) {
    if (imageData is Map<String, dynamic>) {
      return imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      return imageData;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Dialog.fullscreen(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // 背景可点击区域
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
            // 图片轮播
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                final imageUrl = _getImageUrl(widget.images[index]);
                if (imageUrl == null || imageUrl.isEmpty) {
                  return const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.white54,
                    ),
                  );
                }

                return Center(
                  child: InteractiveViewer(
                    panEnabled: true,
                    scaleEnabled: true,
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ),
                      errorWidget: (context, url, error) => const Center(
                        child: Icon(
                          Icons.broken_image,
                          size: 64,
                          color: Colors.white54,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            // 页面指示器
            if (widget.images.length > 1)
              Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            // 关闭按钮
            Positioned(
              top: 50,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}





/// 使用图片的Tab指示器，图片会绘制在选中Tab的下方并水平居中
class ImageTabIndicator extends Decoration {
  final ImageProvider image;
  final Size imageSize;
  final double bottomPadding;

  const ImageTabIndicator({
    required this.image,
    required this.imageSize,
    this.bottomPadding = 4,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _ImageTabIndicatorPainter(
      image: image,
      imageSize: imageSize,
      bottomPadding: bottomPadding,
      onChanged: onChanged,
    );
  }
}

class _ImageTabIndicatorPainter extends BoxPainter {
  final ImageProvider image;
  final Size imageSize;
  final double bottomPadding;

  ImageStream? _imageStream;
  ImageInfo? _imageInfo;
  ImageStreamListener? _imageStreamListener;

  _ImageTabIndicatorPainter({
    required this.image,
    required this.imageSize,
    required this.bottomPadding,
    VoidCallback? onChanged,
  }) : super(onChanged);

  void _resolveImage(ImageConfiguration configuration) {
    final ImageStream newStream = image.resolve(configuration);
    if (_imageStream?.key == newStream.key) {
      return;
    }
    if (_imageStream != null && _imageStreamListener != null) {
      _imageStream!.removeListener(_imageStreamListener!);
    }
    _imageStream = newStream;
    _imageStreamListener ??= ImageStreamListener(_handleImage, onError: _handleError);
    _imageStream!.addListener(_imageStreamListener!);
  }

  void _handleImage(ImageInfo imageInfo, bool synchronousCall) {
    _imageInfo = imageInfo;
    onChanged?.call();
  }

  void _handleError(Object exception, StackTrace? stackTrace) {
    // 忽略加载失败，指示器不绘制
  }

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    if (configuration.size == null) return;

    _resolveImage(configuration);

    final Rect rect = offset & configuration.size!;
    final double dx = rect.left + (rect.width - imageSize.width) / 2;
    final double dy = rect.bottom - bottomPadding - imageSize.height;
    final Rect dstRect = Rect.fromLTWH(dx, dy, imageSize.width, imageSize.height);

    final ImageInfo? info = _imageInfo;
    if (info == null) {
      return; // 图片尚未加载完成
    }

    final Size naturalSize = Size(
      info.image.width.toDouble(),
      info.image.height.toDouble(),
    );
    final Rect srcRect = Offset.zero & naturalSize;

    final Paint paint = Paint()..isAntiAlias = true;
    canvas.drawImageRect(info.image, srcRect, dstRect, paint);
  }

  @override
  void dispose() {
    if (_imageStream != null && _imageStreamListener != null) {
      _imageStream!.removeListener(_imageStreamListener!);
    }
    _imageStream = null;
    _imageInfo = null;
    super.dispose();
  }
}