import 'dart:convert';

import 'package:dlyz_flutter/pages/community/topic_page.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../../components/refresh_header.dart';
import '../../components/vote_widget.dart';
import '../../components/forum_post_item.dart';
import '../../model/emoji.dart';
import '../../net/api/forum_service.dart';
import '../../net/config/http_base_config.dart';
import '../../model/forum_post_list.dart';
import '../../model/forum_category.dart';
import '../../providers/bottom_nav_provider.dart';
import '../../track/track.dart';
import '../../track/forum_track.dart';
import '../../utils/log_util.dart';
import '../../components/cache_image.dart';
import '../../components/video_player_card.dart';
import '../../info/forum_info.dart';
import 'community_detail_page.dart';
import 'user_profile_page.dart';

class ForumPostListPage extends StatefulWidget {
  final ForumCategory category;
  final ScrollController mainScrollController;
  final List<Widget>? headerWidgets;
  final VoidCallback? onRefreshHeader;

  const ForumPostListPage({
    super.key,
    required this.category,
    required this.mainScrollController,
    this.headerWidgets,
    this.onRefreshHeader,
  });

  @override
  State<ForumPostListPage> createState() => _ForumPostListPageState();
}

class _ForumPostListPageState extends State<ForumPostListPage>
    with AutomaticKeepAliveClientMixin {
  bool _isLoading = true;
  String? _error;
  final ForumService _forumService = ForumService();
  late final ScrollController _scrollController = PrimaryScrollController.of(context);
  EasyRefreshController easyRefreshController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true
  );

  // Pagination state
  final List<ForumPost> _posts = [];
  int _currentPage = 1;
  int _totalPage = 1;
  bool _isLoadingMore = false;
  bool _hasMore = true;
  
  // 关注状态管理
  final Set<int> _followingUserIds = <int>{};
  final Set<int> _followLoadingUserIds = <int>{};

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // 使用 WidgetsBinding.instance.addPostFrameCallback 确保在组件完全初始化后再加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadPostList(reset: true);
      }
    });
  }

  // 处理点击圈子Tab事件
  void _handleCommunityPageTap() async {
    if (_isLoadingMore || _isLoading) return;
    if (_scrollController.hasClients) {
      widget.mainScrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
      widget.onRefreshHeader?.call();
      await _loadPostList(reset: true);
      easyRefreshController.finishRefresh();
      easyRefreshController.resetFooter();
    }
  }

  Future<void> _loadPostList({bool reset = false}) async {
    if (!mounted) return;

    LogUtil.d('开始加载帖子列表', tag: 'ForumPostList');

    if (reset) {
      setState(() {
        _isLoading = true;
        _error = null;
        _currentPage = 1;
        _hasMore = true;
      });
    } else {
      if (!_hasMore) return;
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      final response = await _forumService.getPostList(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        categoryId: widget.category.categoryId,
        categoryName: widget.category.name, // 传入分类名称，用于设置scope参数
        page: reset ? 1 : (_currentPage + 1),
        pageSize: 10,
        stype: widget.category.stype,
        context: context,
      );

      if (!mounted) return;

      LogUtil.d('接口响应: code=${response.code}, message=${response.message}', tag: 'ForumPostList');

      if (response.isSuccess && response.data != null) {
        final data = response.data;
        if (data != null) {
          setState(() {
          if (reset) {
            _posts
              ..clear()
              ..addAll(data.pageData);
            // 重置时初始化关注状态
            _followingUserIds.clear();
            for (final post in data.pageData) {
              if (post.user.follow == 1 || post.user.follow == 2) {
                _followingUserIds.add(post.user.userId);
              }
            }
          } else {
            _posts.addAll(data.pageData);
            // 加载更多时添加新的关注状态
            for (final post in data.pageData) {
              if (post.user.follow == 1 || post.user.follow == 2) {
                _followingUserIds.add(post.user.userId);
              }
            }
          }
          _currentPage = data.currentPage;
          _totalPage = data.totalPage;
          _hasMore = _currentPage < _totalPage && data.pageData.isNotEmpty;
          _isLoading = false;
          _isLoadingMore = false;
        });
        }
        LogUtil.d('帖子列表加载成功', tag: 'ForumPostList');
      } else {
        setState(() {
          _error = response.message ?? '获取帖子列表失败';
          _isLoading = false;
          _isLoadingMore = false;
        });
        LogUtil.w('帖子列表加载失败: ${response.message}', tag: 'ForumPostList');
      }
    } catch (e, stackTrace) {
      if (!mounted) return;
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
        _isLoadingMore = false;
      });
      LogUtil.e('帖子列表加载异常', error: e, stackTrace: stackTrace, tag: 'ForumPostList');
    }
  }

  /// 刷新指定帖子的详情数据
  Future<void> _refreshPostDetail(int threadId, int postIndex) async {
    try {
      final response = await _forumService.getPostDetail(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        threadId: threadId,
        context: context,
      );

      if (!mounted) return;

      if (response.isSuccess && response.data != null) {
        final updatedPost = response.data;
        if (updatedPost != null) {
          setState(() {
            // 更新列表中对应帖子的数据
            if (postIndex < _posts.length) {
              _posts[postIndex] = updatedPost;
            }
          });
        }
        LogUtil.d('帖子详情刷新成功', tag: 'ForumPostList');
      } else {
        LogUtil.w('帖子详情刷新失败: ${response.message}', tag: 'ForumPostList');
      }
    } catch (e, stackTrace) {
      LogUtil.e('帖子详情刷新异常', error: e, stackTrace: stackTrace, tag: 'ForumPostList');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer<BottomNavProvider>(
        builder: (context, bottomNavProvider, child) {
          if (bottomNavProvider.isTapCommunityPage()) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _handleCommunityPageTap();
            });
          }

          return Container(
            color: Colors.white,
            margin: const EdgeInsets.only(top: 56),
            child: EasyRefresh(
              controller: easyRefreshController,
              header: const CustomerHeader(),
              onRefresh: () async {
                // 通知父页面刷新 headerWidgets
                widget.onRefreshHeader?.call();
                await _loadPostList(reset: true);
                easyRefreshController.finishRefresh();
                easyRefreshController.resetFooter();
              },
              child: CustomScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                controller: _scrollController,
                slivers: [
                  // 头部组件区域
                  if ((widget.headerWidgets ?? const []).isNotEmpty)
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(children: widget.headerWidgets ?? []),
                      ),
                    ),

                  // 各种状态占位：加载中/错误/空
                  if (_isLoading)
                    const SliverFillRemaining(
                      hasScrollBody: false,
                      child: Center(child: CircularProgressIndicator()),
                    )
                  else if (_error != null)
                    SliverFillRemaining(
                      hasScrollBody: false,
                      child: _buildErrorWidget(),
                    )
                  else if (_posts.isEmpty)
                    // 只有在没有头部组件内容时才显示空状态提示
                      if ((widget.headerWidgets ?? const []).isEmpty)
                        SliverFillRemaining(
                          hasScrollBody: false,
                          child: _buildEmptyWidget(),
                        )
                      else
                      // 有头部组件但没有帖子时，不显示任何额外内容
                        const SliverToBoxAdapter(child: SizedBox.shrink())
                    else ...[
                        // 帖子列表
                        SliverPadding(
                          padding: EdgeInsets.zero,
                          sliver: SliverList(
                            delegate: SliverChildBuilderDelegate(
                                  (context, index) {
                                final post = _posts[index];
                                return _buildPostItem(post, index);
                              },
                              childCount: _posts.length,
                            ),
                          ),
                        ),
                        // 加载更多/没有更多 提示
                        SliverToBoxAdapter(
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Center(
                              child: _isLoadingMore
                                  ? const Padding(
                                padding: EdgeInsets.symmetric(vertical: 12),
                                child: SizedBox(
                                  height: 24,
                                  width: 24,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                              )
                                  : (_hasMore
                                  ? const SizedBox.shrink()
                                  : Text(
                                '没有更多了',
                                style: TextStyle(color: Colors.grey[500], fontSize: 12),
                              )),
                            ),
                          ),
                        ),
                      ],
                ],
              ),
            ),
          );
        }
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _error ?? '加载失败',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadPostList,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.article_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无${widget.category.name}内容',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostItem(ForumPost post, int index) {
    // 判断是否需要显示顶部分割线
    // 如果是第一个item且没有header组件，则不显示分割线
    final bool isFirstItem = index == 0 && (widget.headerWidgets?.isEmpty ?? true);
    
    return ForumPostItem(
      post: post,
      index: index,
      isFirstItem: isFirstItem,
      showVoteWidget: true,
      followingUserIds: _followingUserIds,
      followLoadingUserIds: _followLoadingUserIds,
      onTap: () => _navigateToPostDetail(post),
      onUserTap: (user) => _navigateToUserProfile(user),
      onFollowTap: (user) => _onFollowTap(user),
      onTopicTap: (topicId) => _navigateToTopicPage(topicId),
      onVideoTap: (videoUrl) => _playVideoFullscreen(videoUrl),
      onImageGalleryTap: (context, images, index, {post}) => _showImageGallery(context, images, index, post: post),
      onVoteSuccess: (updatedPost, refreshCallback) {
        // 投票成功后刷新帖子详情
        refreshCallback?.call();
      },
      onRefreshPostDetail: (threadId, postIndex) => _refreshPostDetail(threadId, postIndex),
    );
  }


  /// 全屏播放视频
  void _playVideoFullscreen(String videoUrl) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerFullScreenPage(videoUrl: videoUrl),
      ),
    );
  }

  void _navigateToTopicPage(String topicIdStr) {
    final int? topicId = int.tryParse(topicIdStr);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TopicPage(
          topicName: topicIdStr,
          topicId: topicId,
          description: null,
          avatarUrl: null,
          viewCount: 0,
          likeCount: 0,
          threadCount: 0,
        ),
      ),
    );
  }

  /// 跳转到帖子详情页
  void _navigateToPostDetail(ForumPost post) async {
    final int postIndex = _posts.indexWhere((p) => p.threadId == post.threadId);
    
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommunityDetailPage(post: post),
      ),
    );
    
    // 如果从详情页返回且可能发生了投票，刷新该帖子的数据
    if (result == true || result == null) {
      // result为null表示正常返回，result为true表示明确需要刷新
      if (postIndex != -1) {
        await _refreshPostDetail(post.threadId, postIndex);
      }
    }
  }

  /// 跳转到用户个人资料页面
  void _navigateToUserProfile(ForumUser user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfilePage(user: user),
      ),
    );
  }

  /// 关注用户
  Future<void> _onFollowTap(ForumUser user) async {
    ForumTrack.trackFollowClick();
    // 如果正在关注中，直接返回
    if (_followLoadingUserIds.contains(user.userId)) {
      return;
    }
    
    // 如果已经关注，直接返回
    if (_followingUserIds.contains(user.userId)) {
      return;
    }
    
    // 添加到关注中状态
    setState(() {
      _followLoadingUserIds.add(user.userId);
    });
    
    try {
      final response = await _forumService.followUser(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        toUserId: user.userId,
        context: context,
      );
      
      if (!mounted) return;
      
      if (response.isSuccess) {
        // 关注成功，添加到已关注列表
        setState(() {
          _followingUserIds.add(user.userId);
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已关注 ${user.nickname}'),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        // 关注失败
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message ?? '关注失败'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('关注失败: $e'),
          duration: const Duration(seconds: 2),
        ),
      );
    } finally {
      // 移除关注中状态
      if (mounted) {
        setState(() {
          _followLoadingUserIds.remove(user.userId);
        });
      }
    }
  }

  /// 显示图片画廊（支持左右滑动）
  void _showImageGallery(BuildContext context, List<dynamic> images, int initialIndex, {ForumPost? post}) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        return _ImageGalleryDialog(
          images: images, 
          initialIndex: initialIndex, 
          post: post,
          onViewPost: post != null ? () {
            Navigator.of(context).pop(); // 关闭图片浏览
            _navigateToPostDetail(post); // 跳转到帖子详情
          } : null,
        );
      },
    );
  }



  



}

/// 图片画廊弹窗组件
class _ImageGalleryDialog extends StatefulWidget {
  final List<dynamic> images;
  final int initialIndex;
  final ForumPost? post;
  final VoidCallback? onViewPost;

  const _ImageGalleryDialog({
    required this.images,
    required this.initialIndex,
    this.post,
    this.onViewPost,
  });

  @override
  State<_ImageGalleryDialog> createState() => _ImageGalleryDialogState();
}

class _ImageGalleryDialogState extends State<_ImageGalleryDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  String? _getImageUrl(dynamic imageData) {
    if (imageData is Map<String, dynamic>) {
      return imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      return imageData;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Dialog.fullscreen(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // 背景可点击区域
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
            // 图片轮播
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                final imageUrl = _getImageUrl(widget.images[index]);
                if (imageUrl == null || imageUrl.isEmpty) {
                  return const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.white54,
                    ),
                  );
                }

                return Center(
                  child: InteractiveViewer(
                    panEnabled: true,
                    scaleEnabled: true,
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ),
                      errorWidget: (context, url, error) => const Center(
                        child: Icon(
                          Icons.broken_image,
                          size: 64,
                          color: Colors.white54,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            // 页面指示器
            if (widget.images.length > 1)
              Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            // 关闭按钮
            Positioned(
              top: 50,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
            // 查看帖子按钮
            if (widget.post != null && widget.onViewPost != null)
              Positioned(
                bottom: 50,
                left: 0,
                right: 0,
                child: Center(
                  child: GestureDetector(
                    onTap: widget.onViewPost,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            '查看帖子',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.white,
                            size: 16,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}