import 'package:flutter/material.dart';
import '../../privacy/privacy_manager.dart';
import '../../../webview/webview_dialog.dart';

/// 用户协议弹窗
class UserAgreementDialog extends StatelessWidget {
  const UserAgreementDialog({super.key});

  /// 显示用户协议弹窗
  static Future<bool?> show(BuildContext context) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.8),
      builder: (BuildContext context) {
        return const UserAgreementDialog();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Container(
              padding: const EdgeInsets.only(top: 24, bottom: 20),
              child: const Text(
                '服务协议及隐私保护',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
            ),
            
            // 协议内容
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: RichText(
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.6,
                    color: Color(0xFF666666),
                  ),
                  children: [
                    const TextSpan(
                      text: '为了更好地保障你的合法权益，请你先阅读并同意',
                    ),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () async {
                          final userUrl = await PrivacyManager.userAgreementUrl;
                          _openWebView(
                            context,
                            '用户协议',
                            userUrl.isNotEmpty
                                ? '${userUrl}&isAgree=true&env=dl'
                                : 'https://example.com/user-agreement',
                          );
                        },
                        child: const Text(
                          '《用户协议》',
                          style: TextStyle(
                            color: Color(0xFF4571FB),
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    const TextSpan(text: '和'),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () async {
                          final privacyUrl = await PrivacyManager.privacyPolicyUrl;
                          _openWebView(
                            context,
                            '隐私政策',
                            privacyUrl.isNotEmpty
                                ? '${privacyUrl}&isAgree=true&env=dl'
                                : 'https://example.com/privacy-policy',
                          );
                        },
                        child: const Text(
                          '《隐私政策》',
                          style: TextStyle(
                            color: Color(0xFF4571FB),
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    const TextSpan(
                      text: '，未注册的手机号将自动完成账号注册',
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 按钮区域
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                ),
              ),
              child: Row(
                children: [
                  // 不同意按钮
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop(false);
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                        ),
                      ),
                      child: const Text(
                        '不同意',
                        style: TextStyle(
                          color: Color(0xFF999999),
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),

                  // 中间分割线
                  Container(
                    width: 1,
                    height: 54,
                    color: const Color(0xFFEEEEEE),
                  ),

                  // 同意并继续按钮
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop(true);
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                        ),
                      ),
                      child: const Text(
                        '同意并继续',
                        style: TextStyle(
                          color: Color(0xFF4571FB),
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 打开WebView显示协议内容
  void _openWebView(BuildContext context, String title, String url) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (context) {
        return WebViewDialog(url: url, title: title, showToolBar: false);
      },
    );
  }
}