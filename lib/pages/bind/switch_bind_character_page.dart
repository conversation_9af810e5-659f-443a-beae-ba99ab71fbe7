import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'character_bind_page.dart';
import 'bind_success_page.dart';
import '../../components/game_authorization_dialog.dart';
import '../../providers/user_provider.dart';
import '../../providers/game_circle_provider.dart';
import '../../providers/game_circle_config_provider.dart';
import '../../providers/role_provider.dart';
import '../../model/user.dart';
import '../../model/sub_user.dart';
import '../../services/bind_account_service.dart';
import '../../model/game_role.dart';
import '../../track/circle_track.dart';

class SwitchBindCharacterPage extends StatefulWidget {
  const SwitchBindCharacterPage({super.key});

  @override
  State<SwitchBindCharacterPage> createState() => _SwitchBindCharacterPageState();
}

class _SwitchBindCharacterPageState extends State<SwitchBindCharacterPage> with WidgetsBindingObserver {
  final BindAccountService _bindAccountService = BindAccountService();
  List<SubUserItem> _subUsers = [];
  bool _isLoadingSubUsers = true;

  // 记录游戏授权的trace_id
  String? _currentTraceId;

  @override
  void initState() {
    super.initState();
    // 添加页面可见性监听器
    WidgetsBinding.instance.addObserver(this);
    _loadSubUsers();
  }

  @override
  void dispose() {
    // 移除页面可见性监听器
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        // 应用回到前台时的处理
        print('页面可见：SwitchBindCharacterPage');
        _checkAuthorizationStatus();
        break;
      default:
    }
  }

  /// 加载关联账号列表
  Future<void> _loadSubUsers() async {
    try {
      final response = await _bindAccountService.getSubUserList();
      
      // 直接检查SubUserListResponse的state字段
      if (response.data != null && response.data!.state == 1 && response.data!.data?.list != null) {
        setState(() {
          _subUsers = response.data!.data!.list;
          _isLoadingSubUsers = false;
        });
        debugPrint('成功获取关联账号列表，共${_subUsers.length}个账号');
      } else {
        debugPrint('获取关联账号列表失败: state=${response.data?.state}, msg=${response.data?.msg}');
        setState(() {
          _subUsers = [];
          _isLoadingSubUsers = false;
        });
        
        // 显示错误提示
        Fluttertoast.showToast(
          msg: response.data?.msg ?? '获取关联账号列表失败',
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.CENTER,
        );
      }
    } catch (e) {
      debugPrint('获取关联账号列表异常: $e');
      setState(() {
        _subUsers = [];
        _isLoadingSubUsers = false;
      });
      
      // 显示异常提示
      Fluttertoast.showToast(
        msg: '获取关联账号列表失败，请检查网络连接',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.CENTER,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '选择游戏账号绑定角色',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 37手游官服账号
            const Text(
              '37手游官服账号',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            
            // 账号列表
            if (_isLoadingSubUsers) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(width: 16),
                    Text(
                      '正在加载账号列表...',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ] else if (_subUsers.isNotEmpty) ...[
              ..._subUsers.map((subUser) => _buildSubUserAccountItem(subUser)),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '暂无关联账号',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 12),
            
            // 其他绑定方式
            const Text(
              '其他绑定方式',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            
            // 游戏图标和名称 - 使用圈子配置
            Consumer<GameCircleConfigProvider>(
              builder: (context, configProvider, child) {
                final gameIcon = configProvider.gameIcon;
                final gameName = configProvider.gameName;

                return Center(
                  child: Column(
                    children: [
                      Container(
                        width: 70,
                        height: 70,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(14),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.2),
                              spreadRadius: 1,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(14),
                          child: gameIcon != null
                              ? Image.network(
                                  gameIcon,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: Colors.grey[200],
                                      child: const Icon(
                                        Icons.games,
                                        size: 35,
                                        color: Colors.grey,
                                      ),
                                    );
                                  },
                                )
                              : Container(
                                  color: Colors.grey[200],
                                  child: const Icon(
                                    Icons.games,
                                    size: 35,
                                    color: Colors.grey,
                                  ),
                                ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        gameName ?? '斗罗大陆：魂师对决',
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // 拉起游戏授权绑定按钮
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _launchGameAuthorization,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                            'assets/images/games_icon_white.png',
                            width: 18,
                            height: 18,
                          ),
                        const SizedBox(width: 6),
                        const Text(
                          '拉起游戏授权绑定',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // 叠在右上角的标签
                Positioned(
                  top: -8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Text(
                      '非37官方账号推荐使用',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 9,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // 底部提示文字
            const Center(
              child: Text(
                '选择授权游戏包后游戏内点击【确定】',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubUserAccountItem(SubUserItem subUser) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: GestureDetector(
        onTap: () => _selectSubUserAccount(subUser),
        child: Container(
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color: Color(0xFFF6F7F9),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  subUser.uname,
                  style: const TextStyle(
                    fontSize: 15,
                    color: Colors.black87,
                  ),
                ),
              ),
              if (subUser.isCurrent)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(29, 111, 233, 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    '当前登录',
                    style: TextStyle(
                      fontSize: 11,
                      color: Color.fromRGBO(69, 113, 251, 1),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: Colors.grey[600],
                ),
            ],
          ),
        ),
      ),
    );
  }


  void _selectSubUserAccount(SubUserItem subUser) async {
    CircleTrack.trackOfficialAccountBindRole(extraData: {'current_uid_status': subUser.isCurrent ? 1 : 0});
    // 显示加载状态
    Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CharacterBindPage(
            phoneNumber: subUser.uname,
            uid: subUser.uid,
            type: CharacterBindType.officialAccount,
          ),
        ),
      );
  }

  void _selectAccount(User user, bool isCurrentUser) async {
    if (isCurrentUser) {
      // 当前登录账号，需要检查角色列表
      await _handleCurrentUserSelection(user.muid ?? '', user.loginInfo.muname ?? '');
      return;
    }
    
    // 其他账号，可以切换账号或跳转到空绑定页面
    _showAccountSwitchDialog(user);
  }

  /// 处理当前用户选择逻辑
  Future<void> _handleCurrentUserSelection(String uid, String displayName) async {
    // 显示加载状态
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在查询角色列表...'),
            ],
          ),
        );
      },
    );

    try {
      final roleProvider = Provider.of<RoleProvider>(context, listen: false);
      
      // 查询可绑定的角色列表
      final availableRoles = await roleProvider.checkAvailableRoles(context, uid);
      
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();

      if (availableRoles == null) {
        // 查询失败，显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('查询角色列表失败，请稍后重试'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // 不管是否有可绑定角色，都跳转到角色绑定页面
      // 获取当前用户的已绑定角色列表
      final allUserRoles = await roleProvider.getAllUserRoles(context, uid);
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final currentUser = userProvider.currentUser;
      final boundRoles = currentUser != null 
          ? await roleProvider.getBoundUserRoles(context, user: currentUser) 
          : <GameRoleV2>[];
      
      if (!mounted) return;
      
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CharacterBindPage(
            phoneNumber: displayName,
            uid: uid,
            type: CharacterBindType.officialAccount,
          ),
        ),
      );
    } catch (e) {
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('查询角色列表异常: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showAccountSwitchDialog(User user) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('切换账号'),
          content: Text('是否切换到账号 ${user.loginInfo.muname ?? ''}？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performAccountSwitch(user);
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _performAccountSwitch(User user) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    
    // 显示加载状态
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在切换账号...'),
            ],
          ),
        );
      },
    );
    
    try {
      final result = await userProvider.switchUser(user);
      final success = result.$1;
      final errorMessage = result.$2;
      
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();
      
      if (success) {
        
        // 刷新页面状态
        setState(() {});
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage ?? '切换到账号 ${user.loginInfo.muname ?? ''} 失败，请重新登录'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('切换账号失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _launchGameAuthorization() async {
    CircleTrack.trackGameAuthorizationBindRole(extraData: {
      'gamemethod_source': '选择绑定方式页面'
    });
    // 首先调用authApply获取trace_id
    try {
      final response = await _bindAccountService.authApply();
      if (response.isSuccess && response.data != null) {
        // 记录trace_id
        _currentTraceId = response.data;
        print("记录trace_id: $_currentTraceId");
        
        // 然后显示游戏选择对话框
        GameAuthorizationDialog.show(
          context: context,
          traceId: _currentTraceId, // 传递trace_id给对话框
          onClose: () {
            // 弹窗关闭时的回调，可以在这里处理一些逻辑
            debugPrint('游戏授权绑定弹窗已关闭');
          },
        );
      } else {
        print("获取trace_id失败: ${response.message}");
      }
    } catch (e) {
      print("获取trace_id异常: $e");
    }
  }

  /// 检查授权状态
  Future<void> _checkAuthorizationStatus() async {
    if (_currentTraceId == null || _currentTraceId!.isEmpty) {
      print("没有trace_id，跳过授权状态检查");
      return;
    }

    try {
      print("检查授权状态，trace_id: $_currentTraceId");
      final response = await _bindAccountService.authRet(traceId: _currentTraceId!);
      
      if (response.isSuccess && response.data != null) {
        final authData = response.data!;
        
        if (authData.data != null) {
          final authRetData = authData.data!;
          print("授权状态: ${authRetData.statusDescription} (status: ${authRetData.status})");
          
          if (authRetData.isAuthorized) {
            // 授权通过，跳转到绑定成功页面
            print("账号 ${authRetData.uname} 授权成功");
            _currentTraceId = null;
            _navigateToBindSuccessPage(authRetData.uname, authRetData.uid);
          } else if (authRetData.isExpired) {
            // 授权过期，清空trace_id
            _currentTraceId = null;
            print("授权已过期，清空trace_id");
          }
          // 如果是未操作状态(isPending)，不做任何处理，等待用户操作
        }
      } else {
        print("获取授权状态失败: ${response.message}");
      }
    } catch (e) {
      print("检查授权状态异常: $e");
    }
  }

  /// 跳转到绑定成功页面
  void _navigateToBindSuccessPage(String characterName, int uid) async {
    // 重新加载账号列表
    await _loadSubUsers();
    // 查找匹配的uid
    final matchedUser = _subUsers.where((user) => user.uid == uid.toString()).firstOrNull;
    if (matchedUser != null) {
      // 找到匹配的用户，跳转到角色绑定页面

      CircleTrack.trackBindRoleResultQueryShow(extraData: {'bind_role_method_source': '游戏授权绑定方式'});
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CharacterBindPage(
            phoneNumber: matchedUser.uname,
            uid: matchedUser.uid,
            type: CharacterBindType.gameAuthorization,
          ),
        ),
      );
    }
  }
}