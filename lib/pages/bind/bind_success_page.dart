import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/function_area_provider.dart';
import '../../providers/game_circle_provider.dart';
import '../../webview/web_router.dart';
import '../../track/circle_track.dart';
class BindSuccessPage extends StatelessWidget {
  final String characterName;
  final bool hasGift;

  const BindSuccessPage({
    super.key, 
    required this.characterName,
    this.hasGift = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '绑定角色',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const SizedBox(height: 100),

            // 成功图标
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.check, color: Colors.white, size: 40),
            ),

            const SizedBox(height: 24),

            // 绑定成功文字
            Text(
              hasGift ? '绑定成功，去领取角色绑定礼包' : '绑定成功',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 60),

            // 按钮区域
            Row(
              children: [
                // 继续绑定按钮
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      // 返回到绑定角色页面继续绑定
                      CircleTrack.trackBindRoleShow(extraData: {
                        'bind_role_source': '完成绑定后的继续绑定页面'
                      });
                      Navigator.pop(context);
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(color: Colors.grey[300]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '继续绑定',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // 确认/去领取按钮
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // 返回到上一级页面（可能是社区页面）
                      Navigator.pop(context);
                      Navigator.pop(context);
                      if (hasGift) {
                        // 跳转到礼包领取页面
                        _navigateToGiftCenter(context);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      hasGift ? '去领取' : '确认',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 跳转到礼包中心
  void _navigateToGiftCenter(BuildContext context) {
    final functionAreaProvider = Provider.of<FunctionAreaProvider>(
      context,
      listen: false,
    );
    final gameCircleProvider = Provider.of<GameCircleProvider>(
      context,
      listen: false,
    );

    // 查找礼包中心功能区
    final giftCenterArea = functionAreaProvider.findByModule('gift_center');
    if (giftCenterArea == null) {
      // 如果没有找到礼包中心功能区，显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('礼包中心功能暂不可用'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // 获取tGid
    final selectedGameCircle = gameCircleProvider.selectedGameCircle;
    if (selectedGameCircle == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先选择游戏圈'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final tGid = selectedGameCircle.tgid;

    // 跳转到Web页面
    WebRouter.jumpToWebPage(
      context,
      giftCenterArea.areaEntrance,
      params: {
        'tgid': tGid,
      },
    );
  }
}
