import 'dart:async';
import 'package:flutter/foundation.dart';
import '../utils/permission_utils.dart';
import 'track.dart';

/// 应用退出相关埋点封装类
/// 处理切后台、恢复前台、账号登出等场景的数据上报
class ExitTrack {
  
  /// 切后台（未杀进程）
  /// 当应用从前台切换到后台时调用
  static void trackBackstageSucc({Map<String, dynamic>? extraData}) {
    final data = <String, dynamic>{};
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('backstage_succ', data);
  }
  
  /// 切后台（未杀进程）后回到游戏
  /// 当应用从后台恢复到前台时调用
  static void trackResumeSucc({Map<String, dynamic>? extraData}) {
    final data = <String, dynamic>{};
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('resume_succ', data);
  }

  static Future<void> trackReturnApp() async {
    final appPushStatus = await PermissionUtils.isPermissionGranted(
      PermissionType.notification,
    );
    final data = <String, dynamic>{
      "app_push_status": appPushStatus ? "1" : "0"
    };
    _trackAsync("return_app", data);
  }
  
  /// 账号登出（手动退出）
  /// [logoutType] 登出类型：'切换账号' 或 '手动退出'
  /// [loginType] 登录类型：1（账号密码）、2（登录手机）、3（微信）
  static void trackLogoutSucc({
    required String logoutType,
    required int loginType,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'logout_type': logoutType,
      'login_type': loginType,
    };
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('logout_succ', data);
  }
  
  /// 异步执行埋点的私有方法
  static void _trackAsync(String eventName, Map<String, dynamic> data) {
    // 使用Future.microtask确保异步执行，不阻塞UI
    Future.microtask(() {
      try {
        SqTrackManager.getInstance().track(eventName, data);
        
        if (kDebugMode) {
          print('ExitTrack: 事件上报成功 - $eventName, data: $data');
        }
      } catch (e) {
        if (kDebugMode) {
          print('ExitTrack: 事件上报失败 - $eventName, error: $e');
        }
        // 静默处理埋点异常，避免影响业务逻辑
      }
    });
  }
}

/// 登出类型枚举
class LogoutType {
  static const String switchAccount = '切换账号';
  static const String manualExit = '手动退出';
}

/// 登录类型枚举（用于埋点上报）
class ExitLoginType {
  static const int accountPassword = 1; // 账号密码
  static const int phoneLogin = 2;      // 登录手机
  static const int wechat = 3;          // 微信
}