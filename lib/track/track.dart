import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:thinking_analytics/td_analytics.dart';
import 'package:provider/provider.dart';

import '../config/app_config.dart';
import '../providers/user_provider.dart';
import '../providers/game_circle_provider.dart';
import '../utils/device_info_util.dart';
import 'init_track.dart';

class SqTrackManager {
  static final _taAppId = 'a64b5a07f57d4bee80ef2658d897789d';
  static final _taServerUrl = 'https://ta.shan-yu-tech.com';

  static bool _isInit = false;
  static String? _cachedTgid; // 缓存的当前游戏圈tgid

  //单例生命
  factory SqTrackManager() => _instance;

  SqTrackManager._internal();

  static final SqTrackManager _instance = SqTrackManager._internal();

  /// 隐私授权之后调用
  static Future<void> init() async {
    if (_isInit) {
      return;
    }
    LogUtil.d('SqTrackManager开始初始化');
    _isInit = true;
    //SDK初始化
    await TDAnalytics.init(_taAppId, _taServerUrl);
    TDAnalytics.enableLog(true);
    TDAnalytics.setSuperProperties({});
    TDAnalytics.enableAutoTrack(TDAutoTrackEventType.APP_START |
    TDAutoTrackEventType.APP_END |
    TDAutoTrackEventType.APP_INSTALL |
    TDAutoTrackEventType.APP_CRASH);
    // 设置动态公共属性, 动态公共属性不支持自动采集事件
    TDAnalytics.setDynamicSuperProperties(() {
      return _getDynamicSuperPropertiesSync();
    });
    
    // 通知InitTrack SDK已初始化，开始上报启动事件和待上报事件
    InitTrack.onTrackInitialized();
    InitTrack.trackAppStart();
  }

  ///动态公共属性 - 同步版本
  static Map<String, dynamic> _getDynamicSuperPropertiesSync() {
    Map<String, dynamic> commonParamsMap = DeviceInfoUtil.getCommonParams();
    //如果初始化已经有公共参数了，就直接用初始化的公共参数
    if (commonParamsMap != {}){
      return commonParamsMap;
    }

    final timestamp = (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();

    final deviceInfo = DeviceInfoUtil.getDeviceInfoFormCashed();
    // 构建通用参数 - 只包含同步可获取的数据
    final commonParams = <String, dynamic>{
      'DYNAMIC_DATE': DateTime.now().toUtc(),
      'pid': AppConfig.pid, // 产品ID，根据项目名称设置
      'gid': AppConfig.gid, // 游戏ID，可配置
      'appid': AppConfig.appId,
      'sversion': AppConfig.sversion,
      'gwversion': AppConfig.gwversion,
      'refer': AppConfig.refer,
      'host_sdk_version': AppConfig.hostSdkVersion,
      'time': timestamp,
      'scut': AppConfig.refer,
      'os': deviceInfo['os'] ?? '',
      'dev': deviceInfo['dev'] ?? '',
    };

    final userDataManager = UserDataManager();
    if (userDataManager.isLoggedIn) {
      commonParams['uid'] = userDataManager.currentUserId;
      commonParams['uname'] = userDataManager.currentUname;
    }

    // 添加平台特定参数
    if (deviceInfo['os'] == 'android') {
      commonParams['oaid'] = deviceInfo['oaid'] ?? '';
      commonParams['imei'] = deviceInfo['imei'] ?? '';
    }
    if (deviceInfo['os'] == 'ios') {
      commonParams['idfa'] = deviceInfo['idfa'] ?? '';
      commonParams['idfv'] = deviceInfo['idfv'] ?? '';
    }
    // 注意：由于setDynamicSuperProperties要求同步函数，这里无法获取异步的设备信息
    // 如果需要设备信息，建议在初始化时一次性设置，或者使用其他方式
    return commonParams;
  }


  static SqTrackManager getInstance() {
    return _instance;
  }

  /// 更新当前游戏圈tgid缓存
  /// 在游戏圈切换时调用此方法
  static void updateTgid(String? tgid) {
    if (_cachedTgid != tgid) {
      _cachedTgid = tgid;
      LogUtil.d('SqTrackManager: tgid已更新为: $tgid');
    }
  }

  /// 获取当前缓存的tgid
  static String? getCachedTgid() {
    return _cachedTgid;
  }

  /// 从GameCircleProvider更新tgid（需要提供context）
  static void updateTgidFromProvider(BuildContext context) {
    try {
      final gameCircleProvider = Provider.of<GameCircleProvider>(context, listen: false);
      final tgid = gameCircleProvider.selectedGameCircle?.tgid;
      updateTgid(tgid);
    } catch (e) {
      LogUtil.w('SqTrackManager: 从GameCircleProvider获取tgid失败: $e');
    }
  }

  ///设置账号ID
  static void setUserId(String userId) {
    TDAnalytics.login(userId);
  }

  /// 获取自定义公共参数
  /// 包含tgid等关键信息，用于每次track调用时合并到properties中
  static Map<String, dynamic> _getCustomCommonParams() {
    final userDataManager = UserDataManager();
    final customParams = <String, dynamic>{
      'tgid': _cachedTgid ?? '',
    };
    LogUtil.d("tgidxxxx:${_cachedTgid}");
    
    if (userDataManager.isLoggedIn) {
      customParams['uid'] = userDataManager.currentUserId ?? '';
    }
    
    return customParams;
  }

  Future<void> track(String eventName, [Map<String, dynamic>? properties]) async {
    // 获取自定义公共参数
    final customCommonParams = _getCustomCommonParams();
    
    // 合并自定义公共参数到properties中
    final mergedProperties = <String, dynamic>{};
    mergedProperties.addAll(customCommonParams);
    if (properties != null) {
      mergedProperties.addAll(properties);
    }
    
    TDAnalytics.track(eventName, properties: mergedProperties);
  }
}
