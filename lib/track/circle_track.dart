import 'dart:async';
import 'package:flutter/foundation.dart';
import 'track.dart';

/// 游戏圈相关埋点封装类
/// 处理游戏圈选择、切换、功能点击、角色绑定等场景的数据上报
class CircleTrack {
  // 存储当前页面的定时器，用于60秒间隔上报
  static Timer? _viewShowTimer;
  // 记录当前页面的tgid，用于判断是否还在同一个页面
  static String? _currentPageTgid;
  // 记录当前页面的额外数据
  static Map<String, dynamic>? _currentPageExtraData;
  
  /// 选择游戏圈集合页曝光
  /// 在游戏圈选择弹窗显示时调用
  static void trackGameCircleListShow({Map<String, dynamic>? extraData}) {
    final data = <String, dynamic>{};
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('game_circle_list_show', data);
  }

  /// 点击选择游戏圈子
  /// [tgid] 游戏圈的tgid
  /// [circleName] 游戏圈名称（可选）
  static void trackChooseGameCircle({
    required String tgid,
    String? circleName,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'tgid': tgid,
    };
    
    if (circleName != null && circleName.isNotEmpty) {
      data['circle_name'] = circleName;
    }
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('choose_game_circle', data);
  }

  /// 圈子首页曝光
  /// [tgid] 游戏圈的tgid
  /// [circleName] 游戏圈名称（可选）
  /// 支持60秒间隔重复上报：在同个页面停留60秒会自动再次上报
  static void trackGameCircleShow({
    required String tgid,
    String? circleName,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'tgid': tgid,
    };
    
    if (circleName != null && circleName.isNotEmpty) {
      data['circle_name'] = circleName;
    }
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    // 立即上报
    _trackAsync('game_circle_show', data);
    
    // 启动60秒间隔重复上报机制
    _startViewShowTimer(tgid, circleName, extraData);
  }
  
  /// 启动60秒间隔的页面曝光重复上报定时器
  static void _startViewShowTimer(String tgid, String? circleName, Map<String, dynamic>? extraData) {
    // 如果当前页面的tgid发生变化，取消之前的定时器
    if (_currentPageTgid != tgid) {
      _cancelViewShowTimer();
    }
    
    // 记录当前页面信息
    _currentPageTgid = tgid;
    _currentPageExtraData = extraData != null ? Map<String, dynamic>.from(extraData) : null;
    
    // 启动新的60秒定时器
    _viewShowTimer = Timer.periodic(const Duration(seconds: 60), (timer) {
      // 检查是否还在同一个页面
      if (_currentPageTgid == tgid) {
        final data = <String, dynamic>{
          'tgid': tgid,
          'is_repeat_view': true, // 标记为重复上报
        };
        
        if (circleName != null && circleName.isNotEmpty) {
          data['circle_name'] = circleName;
        }
        
        if (_currentPageExtraData != null) {
          data.addAll(_currentPageExtraData!);
        }
        
        _trackAsync('game_circle_show', data);
        
        if (kDebugMode) {
          print('CircleTrack: 60秒间隔重复上报 - game_circle_show, tgid: $tgid');
        }
      } else {
        // 页面已切换，取消定时器
        timer.cancel();
      }
    });
  }
  
  /// 取消页面曝光重复上报定时器
  static void _cancelViewShowTimer() {
    _viewShowTimer?.cancel();
    _viewShowTimer = null;
    _currentPageTgid = null;
    _currentPageExtraData = null;
  }
  
  /// 停止当前页面的重复上报（在页面切换或退出时调用）
  static void stopCurrentViewShow() {
    _cancelViewShowTimer();
  }

  /// 点击切换游戏圈子按钮
  /// [tgid] 当前游戏圈的tgid
  static void trackSwitchGameCircle({
    required String tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'tgid': tgid,
    };
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('switch_game_circle', data);
  }

  /// 点击代金券入口
  /// [tgid] 游戏圈的tgid
  static void trackClickCoupon({
    required String tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'tgid': tgid,
    };
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('click_coupon', data);
  }

  /// 点击礼包福利按钮
  /// [tgid] 游戏圈的tgid
  static void trackClickGift({
    required String tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'tgid': tgid,
    };
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('click_gift', data);
  }

  /// 点击积分商城按钮
  /// [tgid] 游戏圈的tgid
  static void trackClickPointsMall({
    required String tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'tgid': tgid,
    };
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('click_points_mall', data);
  }

  /// 点击特惠商城按钮
  /// [tgid] 游戏圈的tgid
  static void trackClickDiscountMall({
    required String tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'tgid': tgid,
    };
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('click_discount_mall', data);
  }

  /// 绑定角色页面曝光
  /// [tgid] 游戏圈的tgid
  static void trackBindRoleShow({
    String? tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{};
    if (tgid != null) {
      data['tgid'] = tgid;
    }
    if (extraData != null) {
      data.addAll(extraData);
    }
    _trackAsync('bind_role_show', data);
  }

  /// 选择官方账号方式绑定角色
  /// [tgid] 游戏圈的tgid
  static void trackOfficialAccountBindRole({
    String? tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{};
    if (tgid != null) {
      data['tgid'] = tgid;
    }
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('official_account_bind_role', data);
  }

  /// 选择游戏授权方式绑定角色
  /// [tgid] 游戏圈的tgid
  static void trackGameAuthorizationBindRole({
    String? tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{};
    if (tgid != null) {
      data['tgid'] = tgid;
    }
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('game_authorization_bind_role', data);
  }

  /// "选择游戏包体"弹窗 绑定角色曝光
  /// [tgid] 游戏圈的tgid
  static void trackChooseGamePackPopShow({
    String? tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{};
    if (tgid != null) {
      data['tgid'] = tgid;
    }
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('choose_game_pack_pop_show', data);
  }

  /// "选择游戏包体"弹窗 绑定角色点击打开游戏
  /// [tgid] 游戏圈的tgid
  /// [packageName] 选择的游戏包名（可选）
  static void trackChooseGamePackPopClick({
    required String tgid,
    String? packageName,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'tgid': tgid,
    };
    
    if (packageName != null && packageName.isNotEmpty) {
      data['package_name'] = packageName;
    }
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('choose_game_pack_pop_click', data);
  }

  /// 绑定结果查询页曝光
  /// [tgid] 游戏圈的tgid
  static void trackBindRoleResultQueryShow({
    String? tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{};
    if (tgid != null) {
      data['tgid'] = tgid;
    }
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('bind_role_result_query_show', data);
  }

  /// 点击"未找到角色？试试别的办法"的换个账号按钮
  /// [tgid] 游戏圈的tgid
  static void trackBindRoleChangeAccount({
    String? tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{};
    if (tgid != null) {
      data['tgid'] = tgid;
    }
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('bind_role_change_account', data);
  }

  /// 绑定结果查询页点击"绑定角色"
  /// [tgid] 游戏圈的tgid
  /// [roleInfo] 角色信息（可选）
  static void trackBindRoleResultQueryClick({
    String? tgid,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{};
    if (tgid != null) {
      data['tgid'] = tgid;
    }
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    _trackAsync('bind_role_result_query_click', data);
  }

  /// 异步执行埋点的私有方法
  static void _trackAsync(String eventName, Map<String, dynamic> data) {
    // 使用Future.microtask确保异步执行，不阻塞UI
    Future.microtask(() {
      try {
        SqTrackManager.getInstance().track(eventName, data);
        
        if (kDebugMode) {
          print('CircleTrack: 事件上报成功 - $eventName, data: $data');
        }
      } catch (e) {
        if (kDebugMode) {
          print('CircleTrack: 事件上报失败 - $eventName, error: $e');
        }
        // 静默处理埋点异常，避免影响业务逻辑
      }
    });
  }

  /// 便捷方法：获取当前选中游戏圈的tgid
  /// 需要在调用处传入GameCircleProvider来获取当前tgid
  static String? getCurrentTgid(dynamic gameCircleProvider) {
    try {
      return gameCircleProvider?.selectedGameCircle?.tgid;
    } catch (e) {
      if (kDebugMode) {
        print('CircleTrack: 获取当前tgid失败 - $e');
      }
      return null;
    }
  }

  /// 便捷方法：游戏圈首页曝光（使用Provider获取tgid）
  /// 支持60秒间隔重复上报机制
  static void trackGameCircleShowWithProvider(dynamic gameCircleProvider, {Map<String, dynamic>? extraData}) {
    final tgid = getCurrentTgid(gameCircleProvider);
    if (tgid != null && tgid.isNotEmpty) {
      // 获取圈子名称
      String? circleName;
      try {
        circleName = gameCircleProvider?.selectedGameCircle?.displayName;
      } catch (e) {
        if (kDebugMode) {
          print('CircleTrack: 获取圈子名称失败 - $e');
        }
      }
      
      trackGameCircleShow(tgid: tgid, circleName: circleName, extraData: extraData);
    } else {
      if (kDebugMode) {
        print('CircleTrack: 无法获取tgid，跳过游戏圈首页曝光埋点');
      }
    }
  }

  /// 便捷方法：点击切换游戏圈子按钮（使用Provider获取tgid）
  static void trackSwitchGameCircleWithProvider(dynamic gameCircleProvider, {Map<String, dynamic>? extraData}) {
    final tgid = getCurrentTgid(gameCircleProvider);
    if (tgid != null && tgid.isNotEmpty) {
      trackSwitchGameCircle(tgid: tgid, extraData: extraData);
    } else {
      if (kDebugMode) {
        print('CircleTrack: 无法获取tgid，跳过切换游戏圈子埋点');
      }
    }
  }
}