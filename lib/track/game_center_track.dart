
import 'package:dlyz_flutter/track/track.dart';

enum GameCenterShowSourceType {

  bottomNavigationBar("底部导航栏的游戏按钮"),
  cashClick("代金券去使用按钮");

  const GameCenterShowSourceType(this.value);
  final String value;
}

enum GameCenterButtonStatus {

  download("下载"),
  downloading("下载中"),
  pause("暂停"),
  install("安装"),
  open("打开游戏");

  const GameCenterButtonStatus(this.value);
  final String value;
}

class GameCenterTrack {
  static const String _showSource = "game_center_show_source";
  static const String _tGid = "tgid";
  static const String _buttonStaus = "button_status";

  static trackGameCenterShow(GameCenterShowSourceType source) {
    Map<String, dynamic> data = {};
    data[_showSource] = source.value;
    SqTrackManager.getInstance().track("game_center_show", data);
  }

  static trackGameCenterOpenGame(int tGid, GameCenterButtonStatus status) {
    Map<String, dynamic> data = {};
    data[_tGid] = tGid;
    data[_buttonStaus] = status.value;
    SqTrackManager.getInstance().track("game_center_open_game", data);
  }

  static trackGameCenterExperienceCashClick(int tGid) {
    Map<String, dynamic> data = {};
    data[_tGid] = tGid;
    SqTrackManager.getInstance().track("game_center_experience_cash_click", data);
  }

  static trackGameCenterDownloadGame(int tGid) {
    Map<String, dynamic> data = {};
    data[_tGid] = tGid;
    SqTrackManager.getInstance().track("game_center_download_game", data);
  }

  static trackDownloadGameNoWifi(int tGid) {
    Map<String, dynamic> data = {};
    data[_tGid] = tGid;
    SqTrackManager.getInstance().track("download_game_no_wifi", data);
  }

  static trackDownloadGameNoWifiLater(int tGid) {
    Map<String, dynamic> data = {};
    data[_tGid] = tGid;
    SqTrackManager.getInstance().track("download_game_no_wifi_later", data);
  }

  static trackDownloadButtonClick(int tGid) {
    Map<String, dynamic> data = {};
    data[_tGid] = tGid;
    SqTrackManager.getInstance().track("download_button_click", data);
  }

  static trackPauseButtonClick(int tGid) {
    Map<String, dynamic> data = {};
    data[_tGid] = tGid;
    SqTrackManager.getInstance().track("pause_button_click", data);
  }

  static trackInstallGame(int tGid) {
    Map<String, dynamic> data = {};
    data[_tGid] = tGid;
    SqTrackManager.getInstance().track("install_game", data);
  }

}