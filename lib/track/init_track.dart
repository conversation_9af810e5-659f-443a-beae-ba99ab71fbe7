import 'dart:io';
import 'package:flutter/foundation.dart';
import 'track.dart';

/// 应用初始化相关埋点
/// 处理启动、闪屏、初始化等关键节点的数据上报
class InitTrack {
  static bool _isTrackInitialized = false;
  static final List<Map<String, dynamic>> _pendingEvents = [];

  /// 启动APP埋点
  /// 在数数SDK初始化后立即上报
  static void trackAppStart() {
    final Map<String, dynamic> properties = {};
    
    _trackEvent('start_dlyzapp', properties);
  }

  /// 闪屏结束埋点
  /// 在SplashPage消失时上报
  static void trackSplashEnd() {
    final Map<String, dynamic> properties = {};
    
    _trackEvent('screen_end', properties);
  }

  /// APP初始化开始埋点
  /// 在M层初始化开始时上报
  static void trackAppInitStart() {
    final Map<String, dynamic> properties = {};
    
    // iOS系统需要添加device_info参数
    if (Platform.isIOS) {
      properties['device_info'] = '';
    }
    
    _trackEvent('dlyzapp_init', properties);
  }

  /// APP初始化完成埋点
  /// 在S层初始化成功后上报
  static void trackAppInitSuccess() {
    final Map<String, dynamic> properties = {};
    
    // iOS系统需要添加device_info参数
    if (Platform.isIOS) {
      properties['device_info'] = '';
    }
    
    _trackEvent('dlyzapp_init_succ', properties);
  }

  /// APP初始化失败埋点
  /// 在M层或S层初始化失败时上报
  /// [reasonFail] 失败原因
  static void trackAppInitFail(String reasonFail) {
    final Map<String, dynamic> properties = {
      'reason_fail': reasonFail,
    };
    
    // iOS系统需要添加device_info参数
    if (Platform.isIOS) {
      properties['device_info'] = '';
    }
    
    _trackEvent('dlyzapp_init_fail', properties);
  }

  /// 内部方法：统一处理事件上报
  /// 确保即使页面结束也能正常上报
  static void _trackEvent(String eventId, Map<String, dynamic> properties) {
    try {
      // 检查Track是否已初始化
      if (!_isTrackInitialized) {
        // 如果Track未初始化，将事件加入待上报队列
        _pendingEvents.add({
          'eventId': eventId,
          'properties': properties,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
        
        if (kDebugMode) {
          print('InitTrack: Track未初始化，事件已加入待上报队列 - $eventId');
        }
        return;
      }

      // 立即上报事件
      _reportEvent(eventId, properties);
      
    } catch (e) {
      // 异常情况下也要保证事件不丢失
      _pendingEvents.add({
        'eventId': eventId,
        'properties': properties,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'error': e.toString(),
      });
      
      if (kDebugMode) {
        print('InitTrack: 事件上报异常，已加入待上报队列 - $eventId, error: $e');
      }
    }
  }

  /// 实际执行事件上报
  static void _reportEvent(String eventId, Map<String, dynamic> properties) {
    // 使用异步方式确保不阻塞主线程
    Future.microtask(() {
      try {
        SqTrackManager.getInstance().track(eventId, properties);
        
        if (kDebugMode) {
          print('InitTrack: 事件上报成功 - $eventId, properties: $properties');
        }
      } catch (e) {
        if (kDebugMode) {
          print('InitTrack: 事件上报失败 - $eventId, error: $e');
        }
        
        // 上报失败时，尝试重新加入队列
        _pendingEvents.add({
          'eventId': eventId,
          'properties': properties,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'retryCount': 1,
        });
      }
    });
  }

  /// Track初始化完成后调用，上报所有待上报事件
  static void onTrackInitialized() {
    _isTrackInitialized = true;
    
    if (_pendingEvents.isNotEmpty) {
      if (kDebugMode) {
        print('InitTrack: Track已初始化，开始上报${_pendingEvents.length}个待上报事件');
      }
      
      // 复制列表避免在遍历过程中修改
      final eventsToReport = List<Map<String, dynamic>>.from(_pendingEvents);
      _pendingEvents.clear();
      
      // 延迟执行确保Track完全就绪
      Future.delayed(const Duration(milliseconds: 100), () {
        for (final event in eventsToReport) {
          try {
            final String eventId = event['eventId'] as String;
            final Map<String, dynamic> properties = 
                Map<String, dynamic>.from(event['properties'] as Map);
            
            _reportEvent(eventId, properties);
            
          } catch (e) {
            if (kDebugMode) {
              print('InitTrack: 待上报事件处理失败 - ${event['eventId']}, error: $e');
            }
          }
        }
      });
    }
  }

  /// 应用即将退出时调用，确保所有事件都已上报
  static Future<void> flushPendingEvents() async {
    if (_pendingEvents.isNotEmpty) {
      if (kDebugMode) {
        print('InitTrack: 应用退出前，强制上报${_pendingEvents.length}个待上报事件');
      }
      
      // 同步方式强制上报所有待上报事件
      final eventsToReport = List<Map<String, dynamic>>.from(_pendingEvents);
      _pendingEvents.clear();
      
      for (final event in eventsToReport) {
        try {
          final String eventId = event['eventId'] as String;
          final Map<String, dynamic> properties = 
              Map<String, dynamic>.from(event['properties'] as Map);
          
          // 同步上报，确保在应用退出前完成
          SqTrackManager.getInstance().track(eventId, properties);
          
        } catch (e) {
          if (kDebugMode) {
            print('InitTrack: 强制上报事件失败 - ${event['eventId']}, error: $e');
          }
        }
      }
      
      // 等待一小段时间确保网络请求完成
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  /// 获取待上报事件数量（调试用）
  static int get pendingEventsCount => _pendingEvents.length;

  /// 检查Track是否已初始化（调试用）
  static bool get isTrackInitialized => _isTrackInitialized;

  /// 重置状态（测试用）
  @visibleForTesting
  static void reset() {
    _isTrackInitialized = false;
    _pendingEvents.clear();
  }
}