
import 'package:dlyz_flutter/track/track.dart';
import '../manager/channel_manager.dart';

// 登录账号类型枚举
enum TrackLoginType {
  accountPassword(1, "账号密码"),
  phone(2, "登录手机"),
  wechat(3, "微信"),
  unknown(0, "unknow");

  const TrackLoginType(this.value, this.description);
  final int value;
  final String description;
}

// 登录方式枚举
enum TrackLoginWay {
  unknown(0, "未知"),
  accountPassword(1, "账号密码"),
  phoneSms(2, "手机号+验证码"),
  fastLogin(3, "本机号码一键进入"),
  autoLogin(4, "自动登录"),
  phonePassword(5, "手机号+密码"),
  historyAccount(6, "历史账号快速登录"),
  wechat(7, "微信登录");

  const TrackLoginWay(this.value, this.description);
  final int value;
  final String description;
}

class LoginTrack {
  static const String _loginType = "login_type";
  static const String _loginWay = "login_way";

  static trackLoginInvoke(TrackLoginType loginType, TrackLoginWay loginWay) {
    Map<String, dynamic> data = {};
    data[_loginType] = loginType.value;
    data[_loginWay] = loginWay.value;
    SqTrackManager.getInstance().track("login_invoke", data);
  }


  static trackLoginSuccess(TrackLoginType loginType, TrackLoginWay loginWay) async {
    Map<String, dynamic> data = {};
    data[_loginType] = loginType.value;
    data[_loginWay] = loginWay.value;
    data['notification_status'] = await (ChannelManager().areNotificationsEnabled());
    SqTrackManager.getInstance().track("login_succ", data);
  }

  static trackLoginFail(TrackLoginType loginType, TrackLoginWay loginWay, String reason) {
    Map<String, dynamic> data = {};
    data[_loginType] = loginType.value;
    data[_loginWay] = loginWay.value;
    data["reason_fail"] = reason;
    SqTrackManager.getInstance().track("login_fail", data);
  }


  static trackAliFastLoginInvoke() {
    SqTrackManager.getInstance().track("invoke_ali_fast_login");
  }

  static trackAliFastInvokeSuccess() {
    SqTrackManager.getInstance().track("ali_fast_login_succ");
  }

  static trackAliFastInvokeFail(String reason) {
    SqTrackManager.getInstance().track("ali_fast_login_fail", {"reason_fail": reason});
  }

  static TrackLoginType getLoginType(String type) {
    switch (type) {
      case 'phone':
        return TrackLoginType.phone;
      case 'wechat':
        return TrackLoginType.wechat;
      case 'common':
        return TrackLoginType.accountPassword;
    }
    return TrackLoginType.unknown;
  }
}