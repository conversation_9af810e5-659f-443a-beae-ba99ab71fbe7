import 'dart:async';
import 'package:dlyz_flutter/track/track.dart';
import '../pages/privacy/privacy_manager.dart';

/// 论坛按钮类型枚举
enum ForumButtonType {
  search("btn01", "搜索入口"),
  category("btn02", "点击分类"),
  post("btn03", "点击帖子"),
  report("btn04", "点击举报"),
  follow("btn05", "点击关注");

  const ForumButtonType(this.id, this.name);
  final String id;
  final String name;
}

/// 论坛举报类型枚举
enum ForumReportType {
  post("帖子"),
  comment("评论"),
  user("用户");

  const ForumReportType(this.type);
  final String type;
}

/// 论坛页面视图枚举
enum ForumViewType {
  home("view01", "首页"),
  search("view07", "搜索页"),
  topic("view08", "话题页"),
  collection("view09", "活动集合页");

  const ForumViewType(this.id, this.name);
  final String id;
  final String name;
}

/// 论坛埋点封装类
class ForumTrack {
  static Timer? _onlineDurationTimer;

  /// 启动在线时长追踪
  static void startOnlineDurationTracking() {
    // 取消现有定时器（如果有的话）
    _onlineDurationTimer?.cancel();
    
    // 创建每5分钟执行一次的定时器
    _onlineDurationTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      try {
        // 检查隐私协议是否已同意
        final isPrivacyAgreed = await PrivacyManager.isPrivacyAgreed();
        
        if (isPrivacyAgreed) {
          // 上报在线时长埋点，时间固定为5
          _trackAsync("Online_duration", {"time": 5});
        }
      } catch (e) {
        // 静默处理异常，避免影响业务逻辑
        print('Online duration tracking error: $e');
      }
    });
  }

  /// 停止在线时长追踪
  static void stopOnlineDurationTracking() {
    _onlineDurationTimer?.cancel();
    _onlineDurationTimer = null;
  }
  /// 按钮点击埋点
  static void trackButtonClick(ForumButtonType buttonType, {Map<String, dynamic>? extraData}) {
    final data = <String, dynamic>{
      'btn_id': buttonType.id,
      'btn_name': buttonType.name,
    };
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    // 异步执行埋点
    _trackAsync("btn_click", data);
  }

  /// 搜索点击埋点
  static void trackSearchClick({String? keyword, String? searchType}) {
    final data = <String, dynamic>{};
    
    if (keyword != null) {
      data['keyword'] = keyword;
    }
    if (searchType != null) {
      data['search_type'] = searchType;
    }
    
    // 异步执行埋点
    _trackAsync("click_search", data);
  }

  /// 提交举报埋点
  static void trackSubmitReport({
    required ForumReportType reportType,
    required int targetId,
    required String reason,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'report_type': reportType.type,
      'id': targetId,
      'reason_fail': reason,
    };
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    // 异步执行埋点
    _trackAsync("submit_report", data);
  }

  /// 页面展示埋点
  static void trackViewShow({
    required ForumViewType viewType,
    String? topicId,
    String? collectionId,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'view_id': viewType.id,
      'view_name': viewType.name,
    };
    
    // 如果是话题页，添加topic_id
    if (viewType == ForumViewType.topic && topicId != null) {
      data['topic_id'] = topicId;
    }
    
    // 如果是集合页，添加collection_id
    if (viewType == ForumViewType.collection && collectionId != null) {
      data['collection_id'] = collectionId;
    }
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    // 异步执行埋点
    _trackAsync("view_show", data);
  }

  /// 帖子曝光埋点
  static void trackPostShow({
    required String id,
    required String uidU,
    required String categoryId,
    required String categoryName,
    String? topicId,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'id': id,
      'uid-u': uidU,
      'category_id': categoryId,
      'category_name': categoryName,
    };
    
    if (topicId != null && topicId.isNotEmpty) {
      data['topic_id'] = topicId;
    }
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    // 异步执行埋点
    _trackAsync("post_show", data);
  }

  /// 论坛登录成功埋点
  static void trackForumLoginSuccess({Map<String, dynamic>? extraData}) {
    final data = <String, dynamic>{};
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    // 异步执行埋点
    _trackAsync("forum_login_succ", data);
  }

  /// 论坛登录失败埋点
  static void trackForumLoginFail({
    required String reason,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'reason_fail': reason,
    };
    
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    // 异步执行埋点
    _trackAsync("forum_login_fail", data);
  }

  /// 模块点击埋点
  static void trackModuleClick({
    required String sceneId,
    required String moduleType,
    required String moduleId,
    required String entryId,
    String? categoryId,
    String? categoryName,
    String? moduleTitle,
    String? moduleOuterDesc,
    String? moduleOuterLink,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'scene_id': sceneId,
      'module_type': moduleType,
      'module_id': moduleId,
      'entry_id': entryId,
    };
    
    if (categoryId != null && categoryId.isNotEmpty) {
      data['category_id'] = categoryId;
    }
    if (categoryName != null && categoryName.isNotEmpty) {
      data['category_name'] = categoryName;
    }
    if (moduleTitle != null && moduleTitle.isNotEmpty) {
      data['module_title'] = moduleTitle;
    }
    if (moduleOuterDesc != null && moduleOuterDesc.isNotEmpty) {
      data['module_outer_desc'] = moduleOuterDesc;
    }
    if (moduleOuterLink != null && moduleOuterLink.isNotEmpty) {
      data['module_outer_link'] = moduleOuterLink;
    }
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    // 异步执行埋点
    _trackAsync("module_click", data);
  }

  /// 在线时长埋点
  static void trackOnlineDuration({
    required int duration,
    String? pageName,
    Map<String, dynamic>? extraData,
  }) {
    final data = <String, dynamic>{
      'duration': duration,
    };
    
    if (pageName != null) {
      data['page_name'] = pageName;
    }
    if (extraData != null) {
      data.addAll(extraData);
    }
    
    // 异步执行埋点
    _trackAsync("Online_duration", data);
  }

  /// 异步执行埋点的私有方法
  static void _trackAsync(String eventName, Map<String, dynamic> data) {
    // 使用Future.microtask确保异步执行，不阻塞UI
    Future.microtask(() {
      try {
        SqTrackManager.getInstance().track(eventName, data);
      } catch (e) {
        // 静默处理埋点异常，避免影响业务逻辑
        print('Forum track error: $e');
      }
    });
  }

  /// 便捷方法：搜索入口点击
  static void trackSearchEntryClick() {
    trackButtonClick(ForumButtonType.search);
  }

  /// 便捷方法：分类点击
  static void trackCategoryClick() {
    trackButtonClick(ForumButtonType.category);
  }

  /// 便捷方法：帖子点击
  static void trackPostClick() {
    trackButtonClick(ForumButtonType.post);
  }

  /// 便捷方法：举报点击
  static void trackReportClick() {
    trackButtonClick(ForumButtonType.report);
  }

  /// 便捷方法：关注点击
  static void trackFollowClick() {
    trackButtonClick(ForumButtonType.follow);
  }

  /// 便捷方法：帖子举报
  static void trackPostReport({
    required int threadId,
    required String reason,
  }) {
    trackSubmitReport(
      reportType: ForumReportType.post,
      targetId: threadId,
      reason: reason,
    );
  }

  /// 便捷方法：首页展示
  static void trackHomeViewShow({Map<String, dynamic>? extraData}) {
    trackViewShow(viewType: ForumViewType.home, extraData: extraData);
  }

  /// 便捷方法：搜索页展示
  static void trackSearchViewShow({Map<String, dynamic>? extraData}) {
    trackViewShow(viewType: ForumViewType.search, extraData: extraData);
  }

  /// 便捷方法：话题页展示
  static void trackTopicViewShow({
    required String topicId,
    Map<String, dynamic>? extraData,
  }) {
    trackViewShow(
      viewType: ForumViewType.topic,
      topicId: topicId,
      extraData: extraData,
    );
  }

  /// 便捷方法：活动集合页展示
  static void trackCollectionViewShow({
    required String collectionId,
    Map<String, dynamic>? extraData,
  }) {
    trackViewShow(
      viewType: ForumViewType.collection,
      collectionId: collectionId,
      extraData: extraData,
    );
  }

  /// 便捷方法：首页固定活动模块点击
  static void trackHomeActivityModuleClick({
    required String moduleType,
    required String moduleId,
    required String entryId,
    String? moduleTitle,
    String? moduleOuterDesc,
    String? moduleOuterLink,
    Map<String, dynamic>? extraData,
  }) {
    trackModuleClick(
      sceneId: "首页固定活动",
      moduleType: moduleType,
      moduleId: moduleId,
      entryId: entryId,
      moduleTitle: moduleTitle,
      moduleOuterDesc: moduleOuterDesc,
      moduleOuterLink: moduleOuterLink,
      extraData: extraData,
    );
  }

  /// 便捷方法：分类活动模块点击
  static void trackCategoryActivityModuleClick({
    required String moduleType,
    required String moduleId,
    required String entryId,
    required String categoryId,
    required String categoryName,
    String? moduleTitle,
    String? moduleOuterDesc,
    String? moduleOuterLink,
    Map<String, dynamic>? extraData,
  }) {
    trackModuleClick(
      sceneId: "分类活动",
      moduleType: moduleType,
      moduleId: moduleId,
      entryId: entryId,
      categoryId: categoryId,
      categoryName: categoryName,
      moduleTitle: moduleTitle,
      moduleOuterDesc: moduleOuterDesc,
      moduleOuterLink: moduleOuterLink,
      extraData: extraData,
    );
  }

  /// 便捷方法：集合页模块点击
  static void trackCollectionPageModuleClick({
    required String moduleType,
    required String moduleId,
    required String entryId,
    String? moduleTitle,
    String? moduleOuterDesc,
    String? moduleOuterLink,
    Map<String, dynamic>? extraData,
  }) {
    trackModuleClick(
      sceneId: "集合页",
      moduleType: moduleType,
      moduleId: moduleId,
      entryId: entryId,
      moduleTitle: moduleTitle,
      moduleOuterDesc: moduleOuterDesc,
      moduleOuterLink: moduleOuterLink,
      extraData: extraData,
    );
  }
}