import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/forum_login_info.dart';

/// 论坛数据管理器
/// 负责管理论坛登录数据的全局存储和访问
class ForumDataManager {
  static final ForumDataManager _instance = ForumDataManager._internal();
  factory ForumDataManager() => _instance;
  ForumDataManager._internal();

  // 当前登录的论坛用户信息
  ForumLoginInfo? _currentForumLoginInfo;
  
  // SharedPreferences key
  static const String _forumLoginInfoKey = 'forum_login_info';

  /// 获取当前论坛登录信息
  ForumLoginInfo? get currentForumLoginInfo => _currentForumLoginInfo;

  /// 检查是否已登录论坛
  bool get isForumLoggedIn => _currentForumLoginInfo != null && 
      _currentForumLoginInfo!.accessToken != null && 
      _currentForumLoginInfo!.accessToken!.isNotEmpty;

  /// 获取当前用户ID
  int? get currentUserId => _currentForumLoginInfo?.uid ?? _currentForumLoginInfo?.userId;

  /// 获取访问令牌
  String? get accessToken => _currentForumLoginInfo?.accessToken;

  /// 获取令牌类型
  String? get tokenType => _currentForumLoginInfo?.tokenType;

  /// 获取完整的授权头
  String? get authorizationHeader {
    if (_currentForumLoginInfo?.tokenType != null && 
        _currentForumLoginInfo?.accessToken != null) {
      return '${_currentForumLoginInfo!.tokenType} ${_currentForumLoginInfo!.accessToken}';
    }
    return null;
  }

  /// 保存论坛登录信息
  /// [loginInfo] 登录信息
  /// [persist] 是否持久化到本地存储，默认为true
  Future<void> saveForumLoginInfo(ForumLoginInfo loginInfo, {bool persist = true}) async {
    _currentForumLoginInfo = loginInfo;
    
    if (persist) {
      await _persistLoginInfo(loginInfo);
    }
    
    // 打印日志，便于调试
    print('论坛登录信息已保存: uid=${loginInfo.uid}, accessToken=${loginInfo.accessToken?.substring(0, 10)}...');
  }

  /// 从本地存储加载论坛登录信息
  Future<void> loadForumLoginInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final loginInfoJson = prefs.getString(_forumLoginInfoKey);
      
      if (loginInfoJson != null && loginInfoJson.isNotEmpty) {
        final Map<String, dynamic> loginInfoMap = json.decode(loginInfoJson);
        _currentForumLoginInfo = ForumLoginInfo.fromJson(loginInfoMap);
        
        print('论坛登录信息已从本地加载: uid=${_currentForumLoginInfo?.uid}');
      } else {
        print('本地未找到论坛登录信息');
      }
    } catch (e) {
      print('加载论坛登录信息失败: $e');
      _currentForumLoginInfo = null;
    }
  }

  /// 清除论坛登录信息
  /// [clearPersistence] 是否同时清除本地存储，默认为true
  Future<void> clearForumLoginInfo({bool clearPersistence = true}) async {
    _currentForumLoginInfo = null;
    
    if (clearPersistence) {
      await _clearPersistedLoginInfo();
    }
    
    print('论坛登录信息已清除');
  }

  /// 更新用户头像
  void updateUserAvatar(String avatarUrl) {
    if (_currentForumLoginInfo != null) {
      _currentForumLoginInfo = _currentForumLoginInfo!.copyWith(avatarUrl: avatarUrl);
      _persistLoginInfo(_currentForumLoginInfo!);
    }
  }

  /// 检查登录信息是否过期
  bool isLoginExpired() {
    if (_currentForumLoginInfo == null || _currentForumLoginInfo!.expiresIn == null) {
      return true;
    }
    
    // 这里可以根据实际需求实现过期检查逻辑
    // 例如检查 expiresIn 字段
    return false;
  }

  /// 刷新访问令牌
  /// [newAccessToken] 新的访问令牌
  /// [newRefreshToken] 新的刷新令牌（可选）
  Future<void> refreshAccessToken(String newAccessToken, {String? newRefreshToken}) async {
    if (_currentForumLoginInfo != null) {
      _currentForumLoginInfo = _currentForumLoginInfo!.copyWith(
        accessToken: newAccessToken,
        refreshToken: newRefreshToken ?? _currentForumLoginInfo!.refreshToken,
      );
      
      await _persistLoginInfo(_currentForumLoginInfo!);
      print('访问令牌已刷新');
    }
  }

  /// 持久化登录信息到本地
  Future<void> _persistLoginInfo(ForumLoginInfo loginInfo) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final loginInfoJson = json.encode(loginInfo.toJson());
      await prefs.setString(_forumLoginInfoKey, loginInfoJson);
    } catch (e) {
      print('持久化论坛登录信息失败: $e');
    }
  }

  /// 清除本地存储的登录信息
  Future<void> _clearPersistedLoginInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_forumLoginInfoKey);
    } catch (e) {
      print('清除本地论坛登录信息失败: $e');
    }
  }

  /// 获取格式化的用户信息字符串（用于调试）
  String getDebugInfo() {
    if (_currentForumLoginInfo == null) {
      return '未登录论坛';
    }
    
    return '''
论坛登录信息:
- 用户ID: ${_currentForumLoginInfo!.uid ?? _currentForumLoginInfo!.userId}
- 令牌类型: ${_currentForumLoginInfo!.tokenType}
- 访问令牌: ${_currentForumLoginInfo!.accessToken?.substring(0, 10)}...
- 头像URL: ${_currentForumLoginInfo!.avatarUrl}
- 用户状态: ${_currentForumLoginInfo!.userStatus}
- 是否缺少昵称: ${_currentForumLoginInfo!.isMissNickname}
''';
  }
}