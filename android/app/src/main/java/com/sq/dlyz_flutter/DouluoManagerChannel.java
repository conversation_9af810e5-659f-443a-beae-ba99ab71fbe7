package com.sq.dlyz_flutter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;

import androidx.core.app.NotificationManagerCompat;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.EventChannel;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInstaller;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.provider.Settings;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Queue;
import java.util.Set;

import com.sq.auth.FastLoginManager;
import com.sq.auth.AuthResultCallback;
import com.sq.dlyz_flutter.connectivity.Connectivity;
import com.sq.dlyz_flutter.connectivity.ConnectivityBroadcastReceiver;
import com.sq.dlyz_flutter.flutterdownloader.InstallUtil;
import com.sq.dlyz_flutter.pay.PayCallback;
import com.sq.dlyz_flutter.pay.PayRequest;
import com.sq.dlyz_flutter.pay.PayResult;
import com.sq.dlyz_flutter.pay.PaymentManager;
import com.sq.dlyz_flutter.pay.PaymentType;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.sq.dlyz_flutter.oaid.IdsHelper;
import com.sq.dlyz_flutter.oaid.IdsBean;
import com.sq.dlyz_flutter.ui.WechatMiniGameSheet;
import com.sq.dlyz_flutter.ui.MiniGameOption;

/**
 * Flutter与原生通信管理类
 */
public class DouluoManagerChannel implements MethodCallHandler {
    private static final String TAG = "DouluoManagerChannel";
    private static final String CHANNEL_NAME = "channel.control/dlyz";
    private static final String EVENT_NAME = "channel.control/dlyz_status";
    private final Context mContext;

    private WeakReference<Activity> mWeakActivity;

    // 单例实例
    private static volatile DouluoManagerChannel instance;
    
    // MethodChannel实例，用于回调Flutter
    private MethodChannel methodChannel;
    private EventChannel eventChannel;

    private Connectivity connectivity;
    private ConnectivityBroadcastReceiver connectivityBroadcastReceiver;
    
    // 存储从Flutter传递过来的授权应用列表
    private List<Map<String, Object>> authAppList = new ArrayList<>();

    // 安装任务队列
    private final Queue<InstallTask> installTaskQueue = new LinkedList<>();
    // 正在处理的文件路径集合，用于去重
    private final Set<String> processingFiles = new HashSet<>();
    // Handler用于延迟执行任务
    private final Handler installHandler = new Handler(Looper.getMainLooper());
    // 最后一次安装任务的时间戳
    private long lastInstallTime = 0;
    // 任务执行间隔（毫秒）
    private static final long INSTALL_INTERVAL = 1000; // 1秒

    /**
     * 检查是否有授权应用列表
     */
    public boolean hasAuthApps() {
        boolean hasApps = authAppList != null && !authAppList.isEmpty();
        Log.d(TAG, "检查授权应用列表: " + (hasApps ? "有" + authAppList.size() + "个应用" : "列表为空"));
        return hasApps;
    }

    /**
     * 私有构造函数
     */
    public DouluoManagerChannel(Context context) {
        this.mContext = context;
        ConnectivityManager connectivityManager = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
        connectivity = new Connectivity(connectivityManager);
        connectivityBroadcastReceiver = new ConnectivityBroadcastReceiver(mContext, connectivity);
    }

    /**
     * 获取单例实例
     */
    public static DouluoManagerChannel getInstance(Context context) {
        if (instance == null) {
            synchronized (DouluoManagerChannel.class) {
                if (instance == null) {
                    instance = new DouluoManagerChannel(context);
                }
            }
        }
        return instance;
    }

    /**
     * 注册Flutter通信通道
     */
    public void registerWith(BinaryMessenger messenger, Context context) {
        // 创建MethodChannel
        MethodChannel methodChannel = new MethodChannel(messenger, CHANNEL_NAME);
        EventChannel eventChannel = new EventChannel(messenger, EVENT_NAME);

        // 获取实例并设置MethodCallHandler
        DouluoManagerChannel instance = DouluoManagerChannel.getInstance(context);
        instance.methodChannel = methodChannel;
        instance.eventChannel = eventChannel;
        methodChannel.setMethodCallHandler(instance);
        eventChannel.setStreamHandler(connectivityBroadcastReceiver);

        Log.d(TAG, "DouluoManagerChannel registered successfully");
    }

    public void setCurrentActivity(Activity activity) {
        mWeakActivity = new WeakReference<>(activity);
    }

    @Override
    public void onMethodCall(MethodCall call, @NonNull Result result) {
        // 根据方法名处理不同的调用
        switch (call.method) {
            case "getPlatformVersion":
                handleGetPlatformVersion(call, result);
                break;
            case "checkGameInstalled":
                handleCheckGameInstalled(call, result);
                break;
            case "openInstalledGame":
                handleOpenInstalledGame(call, result);
                break;
            case "installApk":
                handleInstallApk(call, result);
                break;
            case "bindingGame":
                handleBindingGame(call, result);
                break;
            case "jumpToMiniProgram":
                jumpToMiniProgram(call, result);
                break;
            case "checkNetworkType":
                checkNetworkType(call, result);
                break;
            case "openUrl":
                handleOpenUrl(call, result);
                break;
                
            // 新增: 闪验相关方法
            case "setFastLoginConfig":
                handleSetFastLoginConfig(call, result);
                break;
            case "checkFastLoginEnvironment":
                handleCheckFastLoginEnvironment(result);
                break;
            case "initializeFastLogin":
                handleInitializeFastLogin(result);
                break;
            case "doFastLogin":
                handleDoFastLogin(result);
                break;
            case "getAndroidId":
                handleGetAndroid(result);
                break;
            case "getOAID":
                handleGetOAID(result);
                break;
            case "zhiFu":
                handlerPay(call, result);
                break;
            case "areNotificationsEnabled":
                handleAreNotificationsEnabled(result);
                break;
            default:
                // 未实现的方法
                result.notImplemented();
                break;
        }
    }

    /**
     * 获取平台版本
     */
    private void handleGetPlatformVersion(MethodCall call, Result result) {
        try {
            String version = android.os.Build.VERSION.RELEASE;
            result.success("Android " + version);
            Log.d(TAG, "Return platform version: " + version);
        } catch (Exception e) {
            Log.e(TAG, "Failed to get platform version: " + e.getMessage());
            result.error("GET_VERSION_FAILED", "获取平台版本失败", e.getMessage());
        }
    }

    /**
     * 检查游戏是否已安装
     */
    private void handleCheckGameInstalled(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }

            boolean isInstalled = checkAppInstalled(packageName);

            result.success(isInstalled);
            Log.d(TAG, "Check if game installed: " + packageName + " - " + isInstalled);
        } catch (Exception e) {
            Log.e(TAG, "Failed to check game installation: " + e.getMessage());
            result.error("CHECK_INSTALL_FAILED", "检查安装状态失败", e.getMessage());
        }
    }

    /**
     * 打开已安装的游戏
     */
    private void handleOpenInstalledGame(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }
    
            // 获取游戏启动意图
            Intent launchIntent = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                // 添加新任务标志，确保从后台启动时能正确打开
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(launchIntent);
                Log.d(TAG, "Successfully opened game: " + packageName);
                result.success(true);
            } else {
                Log.e(TAG, "No launch intent found for package: " + packageName);
                result.error("LAUNCH_INTENT_NOT_FOUND", "未找到应用启动入口", null);
            }
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "Game not found: " + e.getMessage());
            result.error("GAME_NOT_FOUND", "游戏未安装", e.getMessage());
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied to open game: " + e.getMessage());
            result.error("PERMISSION_DENIED", "没有权限打开应用", e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Failed to open game: " + e.getMessage());
            result.error("OPEN_GAME_FAILED", "打开游戏失败", e.getMessage());
        }
    }

    private void handleInstallApk(MethodCall call, Result result) {
        String filePath = call.argument("filePath");
        if (filePath == null || filePath.isEmpty()) {
            result.error("INVALID_PARAM", "文件路径不能为空", null);
            return;
        }

        final File file = new File(filePath);
        if (!file.exists()) {
            result.error("INVALID_PARAM", "APK文件未找到", null);
            return;
        }

        Activity activity = mWeakActivity.get();
        if (activity == null) {
            return;
        }
        if (activity.isDestroyed() || activity.isFinishing()) {
            return;
        }

        // 将安装请求添加到队列
        synchronized (installTaskQueue) {
            // 检查是否已存在相同路径的任务
            boolean duplicate = false;
            for (InstallTask task : installTaskQueue) {
                if (task.filePath.equals(filePath)) {
                    duplicate = true;
                    break;
                }
            }

            // 如果不是重复任务，则添加到队列
            if (!duplicate) {
                installTaskQueue.offer(new InstallTask(filePath));
                Log.d(TAG, "Added install task to queue: " + filePath);
                // 开始处理队列（如果尚未开始）
                if (!installTaskQueue.isEmpty()) {
                    processInstallQueue();
                } else {
                    Log.d(TAG, "installTaskQueue is Empty");
                }
            } else {
                Log.d(TAG, "Duplicate install task ignored: " + filePath);
            }
        }

        result.success(null);
    }

    // 安装任务内部类
    private static class InstallTask {
        final String filePath;
        final long timestamp;

        InstallTask(String filePath) {
            this.filePath = filePath;
            this.timestamp = System.currentTimeMillis();
        }
    }

    // 处理安装任务队列
    private void processInstallQueue() {
        installHandler.post(new Runnable() {
            @Override
            public void run() {
                synchronized (installTaskQueue) {
                    if (installTaskQueue.isEmpty()) {
                        return;
                    }

                    // 获取队列中的第一个任务
                    InstallTask task = installTaskQueue.peek();

                    if (task == null) return;

                    // 检查是否正在处理相同文件
                    if (processingFiles.contains(task.filePath)) {
                        // 移除任务并处理下一个
                        installTaskQueue.poll();
                        if (!installTaskQueue.isEmpty()) {
                            processInstallQueue();
                        }
                        return;
                    }

                    // 计算延迟时间以确保间隔
                    long currentTime = System.currentTimeMillis();
                    long delay = INSTALL_INTERVAL - (currentTime - lastInstallTime);
                    if (delay < 0) delay = 0;

                    // 延迟执行安装
                    installHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            executeInstallTask(task);
                        }
                    }, delay);
                }
            }
        });
    }

    // 执行安装任务
    private void executeInstallTask(InstallTask task) {
        synchronized (installTaskQueue) {
            // 从队列中移除任务
            installTaskQueue.poll();

            // 标记为正在处理
            processingFiles.add(task.filePath);

            // 更新最后安装时间
            lastInstallTime = System.currentTimeMillis();
        }

        Activity activity = mWeakActivity.get();
        if (activity == null || activity.isDestroyed() || activity.isFinishing()) {
            // 移除处理标记
            processingFiles.remove(task.filePath);
            // 处理下一个任务
            if (!installTaskQueue.isEmpty()) {
                processInstallQueue();
            }
            return;
        }

        try {
            Log.d(TAG, "Executing install task: " + task.filePath + " at time=" + task.timestamp);
            InstallUtil installUtil = new InstallUtil(mContext, activity);
            installUtil.installPackage(task.filePath);
        } catch (Exception e) {
            Log.e(TAG, "Failed to execute install task: " + task.filePath, e);
            onInstallCallback(-2, "", "");
            // 移除处理标记
            processingFiles.remove(task.filePath);
            // 处理下一个任务
            if (!installTaskQueue.isEmpty()) {
                processInstallQueue();
            }
        }
    }

    // 修改 onInstallCallback 方法，在回调后移除处理标记
    public void onInstallCallback(int status, String fileName, String filePath) {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("status", status);
                arguments.put("fileName", fileName);
                methodChannel.invokeMethod("onInstallCallback", arguments);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send onInstallCallback event to Flutter: " + e.getMessage());
        } finally {
            // 移除处理标记
            processingFiles.remove(filePath);
            // 处理下一个任务
            if (!installTaskQueue.isEmpty()) {
                processInstallQueue();
            }
        }
    }

    private void handleBindingGame(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }

            // 获取游戏启动意图
            Intent launchIntent = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                String bindingParams = call.argument("bindingParams");
                // 添加新任务标志，确保从后台启动时能正确打开
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                launchIntent.putExtra("bindingParams", bindingParams);
                mContext.startActivity(launchIntent);
                Log.d(TAG, "Successfully opened game: " + packageName);
                result.success(true);
            } else {
                Log.e(TAG, "No launch intent found for package: " + packageName);
                result.error("LAUNCH_INTENT_NOT_FOUND", "未找到应用启动入口", null);
            }
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "Game not found: " + e.getMessage());
            result.error("GAME_NOT_FOUND", "游戏未安装", e.getMessage());
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied to open game: " + e.getMessage());
            result.error("PERMISSION_DENIED", "没有权限打开应用", e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Failed to open game: " + e.getMessage());
            result.error("OPEN_GAME_FAILED", "打开游戏失败", e.getMessage());
        }
    }

    private void jumpToMiniProgram(MethodCall call, Result result) {
        if (!checkAppInstalled("com.tencent.mm")) {
            Log.w(TAG, "检测到手机没有安装微信，请安装微信后重试");
            return;
        }
        try {
            Integer skipType = call.argument("skip_type");
            if (skipType != null) {
                switch (skipType) {
                    case 1:
                        String appId = call.argument("app_id");
                        IWXAPI api = WXAPIFactory.createWXAPI(mContext, appId);
                        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
                        // 填小程序原始id
                        req.userName = call.argument("mini_program_id");
                        // 拉起小程序页面的可带参路径，不填默认拉起小程序首页，对于小游戏，可以只传入 query 部分，来实现传参效果，如：传入 "?foo=bar"。
                        req.path = call.argument("mini_program_path");
                        // 可选打开 开发版，体验版和正式版
                        req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;
                        if (api != null) {
                            Log.w(TAG, "微信SDK不为空");
                            api.sendReq(req);
                        } else {
                            Log.w(TAG, "微信SDK为null");
                        }
                        break;
                    case 2:
                        String schemeUrl = call.argument("scheme_url");
                        Intent intent = new Intent(Intent.ACTION_VIEW);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        intent.setData(Uri.parse(schemeUrl));
                        mContext.startActivity(intent);
                        break;
                    default:
                        Log.d(TAG, "不支持该类型跳转到微信小程序：" + skipType);
                        break;
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to open mini program: " + e.getMessage());
            result.error("JUMP_MINI_PROGRAM_FAILED", "跳转小程序失败", e.getMessage());
        }

    }

    /**
     * 检查网络状态
     */
    private void checkNetworkType(MethodCall call, Result result) {
        if (connectivity == null) {
            ConnectivityManager connectivityManager = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
            connectivity = new Connectivity(connectivityManager);
            connectivityBroadcastReceiver = new ConnectivityBroadcastReceiver(mContext, connectivity);
        }
        List<String> networkTypes = connectivity.getNetworkTypes();
        result.success(networkTypes);
    }


    /**
     * 设置闪验配置
     */
    private void handleSetFastLoginConfig(MethodCall call, Result result) {
        try {
            Map<String, Object> config = (Map<String, Object>) call.arguments;
            Log.d(TAG, "设置闪验配置: " + config);
            
            Object accessKeyObj = config.get("access_key");
            Object appKeyObj = config.get("app_key");
            Object userAgreementUrlObj = config.get("userAgreementUrl");
            Object privacyPolicyUrlObj = config.get("privacyPolicyUrl");
            Object authAppListObj = config.get("authAppList");
            
            String accessKey = accessKeyObj != null ? accessKeyObj.toString() : null;
            String appKey = appKeyObj != null ? appKeyObj.toString() : null;
            String userAgreementUrl = userAgreementUrlObj != null ? userAgreementUrlObj.toString() : null;
            String privacyPolicyUrl = privacyPolicyUrlObj != null ? privacyPolicyUrlObj.toString() : null;
            
            // 存储授权应用列表
            if (authAppListObj instanceof List) {
                authAppList = (List<Map<String, Object>>) authAppListObj;
                Log.d(TAG, "设置授权应用列表，共" + authAppList.size() + "个应用");
            } else {
                authAppList = new ArrayList<>();
                Log.w(TAG, "未找到有效的授权应用列表");
            }
            
            FastLoginManager fastLoginManager = FastLoginManager.getInstance(mContext);
            
            // 设置协议地址
            if (userAgreementUrl != null && privacyPolicyUrl != null) {
                fastLoginManager.setAgreementUrls(userAgreementUrl, privacyPolicyUrl);
                Log.d(TAG, "设置协议地址 - 用户协议: " + userAgreementUrl);
                Log.d(TAG, "设置协议地址 - 隐私政策: " + privacyPolicyUrl);
            }
            
            // 先设置解密密钥
            if (appKey != null && !appKey.isEmpty()) {
                fastLoginManager.setAppKey(appKey);
                Log.d(TAG, "Setting app key for decryption");
            }
            
            // 再设置accessKey（会使用appKey进行解密）
            if (accessKey != null && !accessKey.isEmpty()) {
                Log.d(TAG, "Setting access key: " + accessKey);

                fastLoginManager.setAccessKey(accessKey);
                
                result.success(true);
            } else {
                Log.w(TAG, "No access key found in config");
                result.success(false);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to set fast login config: " + e.getMessage());
            result.error("SET_CONFIG_FAILED", "设置闪验配置失败", e.getMessage());
        }
    }
    
    /**
     * 检查闪验环境
     */
    private void handleCheckFastLoginEnvironment(Result result) {
        try {
            Log.d(TAG, "Checking fast login environment");
            FastLoginManager.getInstance(mContext).getFastEnv(isSupported -> {
                Log.d(TAG, "Fast login environment check result: " + isSupported);
                result.success(isSupported);
            });
        } catch (Exception e) {
            Log.e(TAG, "Failed to check fast login environment: " + e.getMessage());
            result.error("CHECK_ENV_FAILED", "检查闪验环境失败", e.getMessage());
        }
    }
    
    /**
     * 初始化闪验SDK
     */
    private void handleInitializeFastLogin(Result result) {
        try {
            Log.d(TAG, "Initializing fast login SDK");
            FastLoginManager.getInstance(mContext).initFastAccessKey(new AuthResultCallback() {
                @Override
                public void onSuccess() {
                    Log.d(TAG, "Fast login SDK initialized successfully");
                    result.success(true);
                }
                
                @Override
                public void onFailure(int code, String msg) {
                    Log.e(TAG, "Failed to initialize fast login SDK: " + code + " - " + msg);
                    result.error("INIT_FAILED", msg, String.valueOf(code));
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception during fast login initialization: " + e.getMessage());
            result.error("INIT_EXCEPTION", "初始化异常", e.getMessage());
        }
    }
    
    /**
     * 执行闪验登录
     */
    private void handleDoFastLogin(Result result) {
        try {
            Log.d(TAG, "Starting fast login process");
            final boolean[] responded = new boolean[]{false};
            FastLoginManager.getInstance(mContext).doFastVerifyLogin(new FastLoginManager.FastLoginListener() {
                @Override
                public void onFastLoginSuccess(String token) {
                    Log.d(TAG, "Fast login successful, token: " + token);
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("success", true);
                    resultMap.put("token", token);
                    if (!responded[0]) {
                        responded[0] = true;
                        result.success(resultMap);
                    }
                }
                
                @Override
                public void onFastLoginFail(Bundle bundle) {
                    String code = bundle.getString("CODE", "UNKNOWN");
                    String message = bundle.getString("MESSAGE", "Unknown error");
                    Log.e(TAG, "Fast login failed: " + code + " - " + message);
                    if (!responded[0]) {
                        responded[0] = true;
                        result.error(code, message, null);
                    }
                }
                
                @Override
                public void onFastRelease() {
                    Log.d(TAG, "Fast login UI released");
                    // 若未返回结果，则视为取消，保证Flutter端能够继续流程
                    if (!responded[0]) {
                        responded[0] = true;
                        result.error("CANCEL", "User canceled", null);
                    }
                }
                
                @Override
                public void onVerifyAccount(boolean needOpen) {
                    Log.d(TAG, "Verify account callback: needOpen=" + needOpen);
                    // 账号验证回调可以通过其他方式处理，或者合并到结果中
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception during fast login: " + e.getMessage());
            result.error("FAST_LOGIN_EXCEPTION", "闪验登录异常", e.getMessage());
        }
    }


    /**
     * 获取Android ID
     */
    private void handleGetAndroid(Result result) {
        try {
            String android_id = Settings.System.getString(mContext.getContentResolver(), "android_id");
            Log.d(TAG, "Return android_id: " + android_id);
            result.success(android_id);
        } catch (Exception e) {
            Log.e(TAG, "获取安卓id异常: " + e.getMessage());
            result.error("FAILED_GEI_ANDROIDID", "获取安卓ID失败", e.getMessage());
        }
    }

    /**
     * 获取OAID (Android 10+ 开放匿名设备标识符)
     * 使用回调方式异步返回结果
     */
    private void handleGetOAID(Result result) {
        try {
            Log.d(TAG, "开始获取OAID");
            
            // 检查Android版本是否支持
            if (android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.M) {
                Log.w(TAG, "Android版本6以下，不支持OAID");
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("success", false);
                resultMap.put("oaid", null);
                resultMap.put("aaid", null);
                resultMap.put("vaid", null);
                result.success(resultMap);
                return;
            }
            
            IdsHelper idsHelper = new IdsHelper();
            
            idsHelper.getId(mContext, new IdsHelper.CallBack() {
                @Override
                public void OnOAIDValid(IdsBean ids) {
                    Log.d(TAG, "OAID获取成功: " + ids.toString());
                    
                    // 创建返回结果Map
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("success", true);
                    resultMap.put("oaid", ids.getOaid());
                    resultMap.put("aaid", ids.getAaid());
                    resultMap.put("vaid", ids.getVaid());
                    
                    result.success(resultMap);
                }

                @Override
                public void OnResultCode(int code, String msg) {
                    Log.d(TAG, "OAID获取结果码: " + code + ", 消息: " + msg);
                    
                    if (code == com.bun.miitmdid.core.ErrorCode.INIT_ERROR_RESULT_DELAY) {
                        // 异步获取，等待OnOAIDValid回调
                        Log.d(TAG, "OAID异步获取中，等待回调...");
                        return;
                    }
                    
                    // 其他错误情况，返回空值而不是抛出错误
                    String errorMsg = getOAIDErrorMessage(code, msg);
                    Log.w(TAG, "OAID获取失败: " + errorMsg + ", 返回空值");
                    
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("success", false);
                    resultMap.put("oaid", null);
                    resultMap.put("aaid", null);
                    resultMap.put("vaid", null);
                    result.success(resultMap);
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "获取OAID异常: " + e.getMessage());
            // 返回空值而不是抛出错误
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("success", false);
            resultMap.put("oaid", null);
            resultMap.put("aaid", null);
            resultMap.put("vaid", null);
            result.success(resultMap);
        }
    }
    
    /**
     * 获取OAID错误信息
     */
    private String getOAIDErrorMessage(int code, String msg) {
        switch (code) {
            case 1008009: // ErrorCode.INIT_ERROR_DEVICE_NOSUPPORT
                return "设备不支持OAID";
            case 1008003: // ErrorCode.INIT_ERROR_LOAD_CONFIGFILE
                return "加载配置文件出错";
            case 1008004: // ErrorCode.INIT_ERROR_MANUFACTURER_NOSUPPORT
                return "设备厂商不支持OAID";
            case 1008002: // ErrorCode.INIT_HELPER_CALL_ERROR
                return "反射调用出错";
            case 1008110:
                return "OAID获取异常: " + msg;
            default:
                return "未知错误码: " + code + ", 消息: " + msg;
        }
    }

    private void handlerPay(MethodCall call, Result result) {
        String payType = call.argument("zhiFuType");
        String tradeInfo = call.argument("tradeInfo");
        String orderId = call.argument("orderId");
        if (payType == null || payType.isEmpty()) {
            result.error("ZHIFU_FAILED", "支付类型不能为空", null);
            return;
        }
        if (tradeInfo == null || tradeInfo.isEmpty()) {
            result.error("ZHIFU_FAILED", "支付数据不能为空", null);
            return;
        }
        Activity activity = mWeakActivity.get();
        if (activity == null) {
            return;
        }
        if (activity.isDestroyed() || activity.isFinishing()) {
            return;
        }
        Log.d(TAG, "开始执行支付: " + payType + ", 订单id: " + orderId + ", tradeInfo: " + tradeInfo);
        PaymentType paymentType = PaymentType.fromCode(payType);
        if (paymentType == PaymentType.UNKNOWN) {
            result.error("ZHIFU_FAILED", "不支持的支付类型", null);
            return;
        }
        PayRequest payRequest = new PayRequest(
                tradeInfo,
                orderId == null ? "" : orderId,
                new HashMap<>()
        );
        PaymentManager.getInstance().pay(activity, paymentType, payRequest, new PayCallback() {
            @Override
            public void onPaySuccess(@NonNull PayResult payResult) {
                result.success(true);
            }

            @Override
            public void onPayFailed(@NonNull PayResult payResult) {
                String errorMsg = "支付失败: code = " + payResult.getCode() + ", message = " + payResult.getMessage();
                result.error("ZHIFU_FAILED", errorMsg, errorMsg);
            }

            @Override
            public void onPayCancelled(@NonNull PayResult payResult) {
                result.error("ZHIFU_CANCELLED", payResult.getMessage(), payResult.getMessage());
            }
        });
    }
    
    /**
     * 闪验UI关闭按钮点击回调
     */
    public void onFastLoginCloseClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "close_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login close event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send close event to Flutter: " + e.getMessage());
        }
    }
    
    /**
     * 闪验UI返回按钮点击回调
     */
    public void onFastLoginBackClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "back_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login back event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send back event to Flutter: " + e.getMessage());
        }
    }
    
    /**
     * 忘记密码点击回调
     */
    public void onFastLoginForgetPasswordClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "forget_password_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login forget password event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send forget password event to Flutter: " + e.getMessage());
        }
    }
    
    /**
     * 微信登录点击回调（供闪验自定义UI按钮调用）
     */
    public void onFastLoginWeChatClicked() {
        try {
            // 获取当前Activity - 使用Activity栈获取栈顶Activity
            Activity currentActivity = ActivityStackManager.getInstance().getTopActivity();
            if (currentActivity == null || currentActivity.isFinishing() || currentActivity.isDestroyed()) {
                Log.e(TAG, "Activity is null or finishing, cannot show wechat dialog");
                return;
            }
            List<MiniGameOption> gameOptions = createGameOptionsFromAuthAppList();
            // 显示微信小游戏选择弹窗
            WechatMiniGameSheet.show(currentActivity, gameOptions, option -> {
                Log.d(TAG, "User selected game: " + option.getTitle());
                // 处理游戏选择逻辑
                handleGameSelection(option);
            });
            Log.d(TAG, "Wechat mini game sheet shown");
        } catch (Exception e) {
            Log.e(TAG, "Failed to show wechat dialog: " + e.getMessage());
        }
    }
    
    /**
     * 从授权应用列表创建游戏选项数据
     */
    private List<MiniGameOption> createGameOptionsFromAuthAppList() {
        List<MiniGameOption> options = new ArrayList<>();
        
        if (authAppList != null && !authAppList.isEmpty()) {
            for (Map<String, Object> authApp : authAppList) {
                String name = authApp.get("name") != null ? authApp.get("name").toString() : "";
                String icon = authApp.get("icon") != null ? authApp.get("icon").toString() : null;
                String wechatMinigramId = authApp.get("wechat_minigram_id") != null ? authApp.get("wechat_minigram_id").toString() : "";
                String tgid = authApp.get("tgid") != null ? authApp.get("tgid").toString() : "";
                
                options.add(new MiniGameOption(name, icon, wechatMinigramId, tgid));
                Log.d(TAG, "添加授权应用: " + name + ", tgid: " + tgid);
            }
        }
        
        return options;
    }
    
    /**
     * 处理游戏选择
     */
    private void handleGameSelection(MiniGameOption option) {
        try {
            // 这里应该实现类似Flutter中WechatMiniGameSheet的点击逻辑

            Log.d(TAG, "Handling game selection for: " + option.getTitle());
            Log.d(TAG, "Mini Program ID: " + option.getMiniProgramId());
            Log.d(TAG, "TGID: " + option.getTgid());
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "wechat_game_selected");
                arguments.put("game_title", option.getTitle());
                arguments.put("mini_program_id", option.getMiniProgramId());
                arguments.put("tgid", option.getTgid());
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to handle game selection: " + e.getMessage());
        }
    }

    /**
     * 账号密码登录点击回调（供闪验自定义UI按钮调用）
     */
    public void onFastLoginAccountLoginClicked() {
        try {
            // 关闭闪验授权页，避免覆盖Flutter界面
            FastLoginManager.getInstance(mContext).quitLoginPage();
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "account_login_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login account login event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send account login event to Flutter: " + e.getMessage());
        }
    }

    /**
     * 其他手机号登录点击回调（供闪验自定义UI按钮调用）
     */
    public void onFastLoginOtherPhoneClicked() {
        try {
            // 关闭闪验授权页，避免覆盖Flutter界面
            FastLoginManager.getInstance(mContext).quitLoginPage();
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "other_phone_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login other phone event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send other phone event to Flutter: " + e.getMessage());
        }
    }

    /**
     * 检查授权应用状态（供原生调用Flutter方法）
     */
    public void checkAuthAppStatusIfNeeded() {
        try {
            if (methodChannel != null) {
                Log.d(TAG, "调用Flutter的checkAuthAppStatusIfNeeded方法");
                methodChannel.invokeMethod("checkAuthAppStatusIfNeeded", null, new Result() {
                    @Override
                    public void success(@Nullable Object result) {
                        Log.d(TAG, "Flutter checkAuthAppStatusIfNeeded调用成功: " + result);
                        // 如果需要，可以根据结果处理闪验Activity的关闭
                        if (result instanceof Boolean && (Boolean) result) {
                            // 登录成功，关闭闪验Activity
                            closeFastLoginActivity();
                        }
                    }

                    @Override
                    public void error(@NonNull String errorCode, @Nullable String errorMessage, @Nullable Object errorDetails) {
                        Log.e(TAG, "Flutter checkAuthAppStatusIfNeeded调用失败: " + errorCode + " - " + errorMessage);
                    }

                    @Override
                    public void notImplemented() {
                        Log.w(TAG, "Flutter checkAuthAppStatusIfNeeded方法未实现");
                    }
                });
            } else {
                Log.w(TAG, "MethodChannel为null，无法调用Flutter方法");
            }
        } catch (Exception e) {
            Log.e(TAG, "调用Flutter checkAuthAppStatusIfNeeded方法异常: " + e.getMessage());
        }
    }

    /**
     * 关闭闪验Activity
     */
    private void closeFastLoginActivity() {
        try {
            Log.d(TAG, "正在关闭闪验Activity");
            FastLoginManager.getInstance(mContext).quitLoginPage();
            Log.d(TAG, "闪验Activity关闭完成");
        } catch (Exception e) {
            Log.e(TAG, "关闭闪验Activity异常: " + e.getMessage());
        }
    }

    public void onBindingSuccess(String bindingRespData) {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("bindingRespData", bindingRespData == null ? "" : bindingRespData);
                methodChannel.invokeMethod("onBindingSuccess", arguments);
                Log.d(TAG, "Binding success event sent to Flutter bindingRespData = " + bindingRespData);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send binding success event to Flutter: " + e.getMessage());
        }
    }

    /**
     * 检查应用是否安装
     */
    private boolean checkAppInstalled(String packageName) {
        if (packageName == null || packageName.isEmpty()) {
            return false;
        }

        PackageManager packageManager = mContext.getPackageManager();
        try {
            // 尝试获取包信息，如果不存在会抛出NameNotFoundException
            packageManager.getPackageInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            // 应用未安装
            return false;
        }
    }


    /**
     * 检查通知权限是否开启
     */
    private void handleAreNotificationsEnabled(Result result) {
        try {
            boolean enabled = areNotificationsEnabled();
            Log.d(TAG, "Notifications enabled: " + enabled);
            result.success(enabled);
        } catch (Exception e) {
            Log.e(TAG, "Failed to check notifications enabled: " + e.getMessage());
            result.error("CHECK_NOTIFICATIONS_FAILED", "检查通知权限失败", e.getMessage());
        }
    }

    /**
     * 检查应用通知权限是否开启
     */
    private boolean areNotificationsEnabled() {
        if (mContext == null) {
            return false;
        }
        return NotificationManagerCompat.from(mContext).areNotificationsEnabled();
    }

    /**
     * 打开外部浏览器
     */
    private void handleOpenUrl(MethodCall call, Result result) {
        try {
            String url = call.argument("url");
            if (url == null || url.isEmpty()) {
                result.error("INVALID_PARAM", "URL不能为空", null);
                return;
            }

            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(intent);
            Log.d(TAG, "Successfully opened URL: " + url);
            result.success(true);
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "No browser found: " + e.getMessage());
            result.error("BROWSER_NOT_FOUND", "未找到浏览器应用", e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Failed to open URL: " + e.getMessage());
            result.error("OPEN_URL_FAILED", "打开URL失败", e.getMessage());
        }
    }
}
