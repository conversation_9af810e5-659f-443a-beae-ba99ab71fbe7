//
//  DouluoManagerChannel.swift
//  Runner
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/7/28.
//
import Flutter
import UIKit
import WechatOpenSDK
import ATAuthSDK
import AdSupport
import AppTrackingTransparency
import UserNotifications

/// Flutter与iOS原生通信管理类
class DouluoManagerChannel: NSObject, FlutterStreamHandler {
    /// 单例实例
    static let shared = DouluoManagerChannel()
    
    // MARK: - Debug Logging
    /// 调试日志开关，默认关闭
    static var debugLogEnabled: Bool = true
    
    /// 调试日志打印函数
    /// - Parameters:
    ///   - message: 日志消息
    ///   - function: 函数名（自动获取）
    ///   - line: 行号（自动获取）
    private static func debugLog(_ message: String, function: String = #function, line: Int = #line) {
        if debugLogEnabled {
            NSLog("[DouluoManagerChannel][\(function):\(line)] \(message)")
        }
    }
    
    /// 通信通道名称，必须与Flutter端和Android端保持一致
    private static let channelName = "channel.control/dlyz"
    private static let eventName = "channel.control/dlyz_status"
    
    /// Flutter方法通道
    private var methodChannel: FlutterMethodChannel?
    private var eventChannel: FlutterEventChannel?
    
    /// 用于发送事件到Flutter端
    private var flutterEventSink: FlutterEventSink?
    
    private var connectivityProvider: ConnectivityProvider = PathMonitorConnectivityProvider()
    private var eventSink: FlutterEventSink?
    
    private var flutterEngine: FlutterEngine?
    
    /// 初始化通道
    func setup(with messenger: FlutterBinaryMessenger) {
        DouluoManagerChannel.debugLog("初始化Flutter通信通道")
        methodChannel = FlutterMethodChannel(name: DouluoManagerChannel.channelName, binaryMessenger: messenger)
        eventChannel = FlutterEventChannel(name: DouluoManagerChannel.eventName,binaryMessenger: messenger)
        
        methodChannel?.setMethodCallHandler(handleMethodCall)
        eventChannel?.setStreamHandler(self)
        DouluoManagerChannel.debugLog("Flutter通信通道初始化完成")
    }
    
    /// 处理Flutter调用原生方法
    private func handleMethodCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("收到Flutter方法调用: \(call.method)")
        switch call.method {
        case "getPlatformVersion":
            handleGetPlatformVersion(result: result)
        case "checkGameInstalled":
            handleCheckGameInstalled(call: call, result: result)
        case "openInstalledGame":
            handleOpenInstalledGame(call: call, result: result)
        case "openUrl":
            handleOpenUrl(call: call, result: result)
        case "jumpToMiniProgram":
            jumpToMiniProgram(call: call, result: result)
        case "bindingGame":
            handleBindingGame(call: call, result: result)
        case "checkNetworkType":
            checkNetworkType(call: call, result: result)
        case "setFastLoginConfig":
            configFlashVerify(call: call, result: result)
        case "checkFastLoginEnvironment":
            checkFastLoginEnvironment(call: call, result: result)
        case "doFastLogin":
            doFastLogin(call: call, result: result)
        case "cancelOneKeyLogin":
            cancelOneKeyLogin(call: call, result: result)
        case "getIDFA":
            getIDFA(result: result)
        case "requestTrackingPermission":
            requestTrackingPermission(result: result)
        case "areNotificationsEnabled":
            areNotificationsEnabled(result: result)
        default:
            DouluoManagerChannel.debugLog("未实现的方法: \(call.method)")
            result(FlutterMethodNotImplemented)
        }
    }
    
    public func onListen(
      withArguments _: Any?,
      eventSink events: @escaping FlutterEventSink
    ) -> FlutterError? {
        eventSink = events
        connectivityProvider.start()
        // Update this to handle a list
        connectivityUpdateHandler(connectivityTypes: connectivityProvider.currentConnectivityTypes)
        return nil
    }
    
    public func onCancel(withArguments _: Any?) -> FlutterError? {
        connectivityProvider.stop()
        eventSink = nil
        return nil
    }
    
    private func connectivityUpdateHandler(connectivityTypes: [ConnectivityType]) {
        DispatchQueue.main.async {
            self.eventSink?(self.statusFrom(connectivityTypes: connectivityTypes))
        }
    }
    
    /// 获取平台版本
    private func handleGetPlatformVersion(result: FlutterResult) {
        let version = UIDevice.current.systemVersion
        DouluoManagerChannel.debugLog("获取平台版本: iOS \(version)")
        result("iOS \(version)")
        print("Return platform version: \(version)")
    }
    
    /// 检查游戏是否已安装
    private func handleCheckGameInstalled(call: FlutterMethodCall, result: FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let packageName = args["packageName"] as? String else {
            DouluoManagerChannel.debugLog("检查游戏安装失败: 包名参数无效")
            result(FlutterError(code: "INVALID_PARAM", message: "包名不能为空", details: nil))
            return
        }

        DouluoManagerChannel.debugLog("检查游戏是否安装: \(packageName)")
        // 检查应用是否安装
        let isInstalled = checkAppInstalled(packageName: packageName)
        DouluoManagerChannel.debugLog("游戏安装检查结果: \(packageName) - \(isInstalled)")
        result(isInstalled)
        print("Check if game installed: \(packageName) - \(isInstalled)")
    }

    /// 打开已安装的游戏
    private func handleOpenInstalledGame(call: FlutterMethodCall, result: FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let packageName = args["packageName"] as? String else {
            DouluoManagerChannel.debugLog("打开游戏失败: 包名参数无效")
            result(FlutterError(code: "INVALID_PARAM", message: "包名不能为空", details: nil))
            return
        }
        
        DouluoManagerChannel.debugLog("尝试打开游戏: \(packageName)")
        // 尝试打开应用
        if openApp(packageName: packageName) {
            DouluoManagerChannel.debugLog("成功打开游戏: \(packageName)")
            result(true)
            print("Successfully opened game: \(packageName)")
        } else {
            DouluoManagerChannel.debugLog("打开游戏失败: \(packageName)")
            result(FlutterError(code: "LAUNCH_FAILED", message: "无法打开应用", details: nil))
            print("Failed to open game: \(packageName)")
        }
    }
    
    /// 检查应用是否安装
    private func checkAppInstalled(packageName: String) -> Bool {
        guard let url = URL(string: "\(packageName)://") else { return false }
        return UIApplication.shared.canOpenURL(url)
    }
    
    /// 打开应用
    private func openApp(packageName: String) -> Bool {
        guard let url = URL(string: "\(packageName)://") else { return false }
        
        if #available(iOS 10.0, *) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        } else {
            UIApplication.shared.openURL(url)
        }
        return true
    }
    
    /// 打开URL
    private func handleOpenUrl(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let urlString = args["url"] as? String,
              let url = URL(string: urlString) else {
            DouluoManagerChannel.debugLog("打开URL失败: URL无效")
            NSLog("handleOpenUrl: URL无效")
            result(FlutterError(code: "INVALID_PARAM", message: "URL无效", details: nil))
            return
        }

        DouluoManagerChannel.debugLog("尝试打开URL: \(urlString)")
        if UIApplication.shared.canOpenURL(url) {
            DouluoManagerChannel.debugLog("可以打开URL: \(urlString)")
            NSLog("handleOpenUrl: 可以打开URL")
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(url, options: [:]) {
                    success in
                    DouluoManagerChannel.debugLog("打开URL结果: \(success)")
                    result(true)
                }
            } else {
                let success = UIApplication.shared.openURL(url)
                DouluoManagerChannel.debugLog("打开URL结果: \(success)")
                result(true)
            }
        } else {
            DouluoManagerChannel.debugLog("无法打开URL: \(urlString)")
            result(FlutterError(code: "CANNOT_OPEN", message: "无法打开该URL", details: nil))
        }
    }
    
    /// 跳转小程序
    private func jumpToMiniProgram(call: FlutterMethodCall, result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("跳转小程序请求")
        if (!WXApi.isWXAppInstalled()) {
            DouluoManagerChannel.debugLog("微信未安装")
            NSLog("检测到手机没有安装微信，请安装微信后重试")
            result(FlutterError(code: "JUMP_MINI_PROGRAM_FAILED", message: "检测到手机没有安装微信，请安装微信后重试", details: nil))
            return
        }
        do {
            guard let args = call.arguments as? [String: Any],
                  let skipType = args["skip_type"] as? Int else {
                result(FlutterError(code: "JUMP_MINI_PROGRAM_FAILED", message: "不支持该类型跳转到微信小程序", details: nil))
                return
            }
            switch skipType {
                case 1:
                    DouluoManagerChannel.debugLog("跳转小程序方式1: 使用userName和path")
                    guard let userName = args["mini_program_id"] as? String,
                          let path = args["mini_program_path"] as? String else {
                        DouluoManagerChannel.debugLog("小程序参数无效")
                        result(FlutterError(code: "JUMP_MINI_PROGRAM_FAILED", message: "小程序参数不能为空", details: nil))
                        return
                    }
                    let miniProgramType = WXMiniProgramType.release
                    let miniReq = WXLaunchMiniProgramReq.object()
                    miniReq.userName = userName
                    miniReq.path = path
                    miniReq.miniProgramType = miniProgramType
                    WXApi.send(miniReq) { success in
                        DouluoManagerChannel.debugLog("打开小程序结果: \(success ? "成功" : "失败")")
                        NSLog("打开小程序：\(success ? "成功" : "失败")")
                        result(success)
                    }
                case 2:
                    DouluoManagerChannel.debugLog("跳转小程序方式2: 使用scheme_url")
                    guard let scheme = args["scheme_url"] as? String else {
                        DouluoManagerChannel.debugLog("scheme_url参数无效")
                        result(FlutterError(code: "JUMP_MINI_PROGRAM_FAILED", message: "scheme_url不能为空", details: nil))
                        return
                    }
                    guard let url = URL(string: scheme) else {
                        result(FlutterError(code: "JUMP_MINI_PROGRAM_FAILED", message: "scheme_url格式错误", details: nil))
                        return
                    }
                    if !UIApplication.shared.canOpenURL(url) {
                        result(FlutterError(code: "JUMP_MINI_PROGRAM_FAILED", message: "未安装微信", details: nil))
                        return
                    }
                    UIApplication.shared.open(url, options: [:]) { _ in
                    }
                default:
                    result(FlutterError(code: "JUMP_MINI_PROGRAM_FAILED", message: "跳转小程序失败", details: nil))
                    return
            }
        }
    }

    /// 游戏绑定方法
    private func handleBindingGame(call: FlutterMethodCall, result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("游戏绑定请求")
        print("call.arguments: \(String(describing: call.arguments))")
        
        guard let args = call.arguments as? [String: Any],
              let packageName = args["packageName"] as? String,
              let bindingParamsJson = args["bindingParams"] as? String else {
            DouluoManagerChannel.debugLog("游戏绑定参数验证失败")
            NSLog("参数验证失败")
            result(FlutterError(code: "INVALID_PARAM", message: "参数错误：包名或绑定参数不能为空", details: nil))
            return
        }
        
        DouluoManagerChannel.debugLog("开始游戏绑定: packageName=\(packageName), params=\(bindingParamsJson)")
        NSLog("开始游戏绑定: packageName=\(packageName), params=\(bindingParamsJson)")
        
        // 构造URL Scheme，按照新规范：{包名}://?type=dlBingGame&其他参数
        let encodedParams = bindingParamsJson.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let urlString = "\(packageName)://?type=dlBingGame&params=\(encodedParams)"
        
        guard let url = URL(string: urlString) else {
            result(FlutterError(code: "INVALID_URL", message: "无效的URL Scheme", details: nil))
            return
        }
        
        // 检查是否可以打开该应用
        // if !UIApplication.shared.canOpenURL(url) {
        //     result(FlutterError(code: "APP_NOT_INSTALLED", message: "目标游戏应用未安装", details: urlString))
        //     NSLog("游戏应用未安装: \(packageName)")
        //     return
        // }
        
        // 打开游戏应用并传递绑定参数
        if #available(iOS 10.0, *) {
            UIApplication.shared.open(url, options: [:]) { success in
                if success {
                    DouluoManagerChannel.debugLog("成功启动游戏绑定: \(packageName)")
                    NSLog("成功启动游戏绑定: \(packageName)")
                    result(true)
                } else {
                    DouluoManagerChannel.debugLog("启动游戏绑定失败: \(packageName)")
                    NSLog("启动游戏绑定失败: \(packageName)")
                    result(FlutterError(code: "LAUNCH_FAILED", message: "启动游戏应用失败", details: nil))
                }
            }
        } else {
            result(FlutterError(code: "LAUNCH_FAILED", message: "启动游戏应用失败", details: nil))
        }
    }
    
    private func configFlashVerify(call: FlutterMethodCall, result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("配置闪验SDK")
        print("call.arguments: \(String(describing: call.arguments))")
        
        guard let args = call.arguments as? [String: Any],
              let secrect = args["access_key"] as? String, secrect != "", let appkey = args["app_key"] as? String, appkey != "" else {
            result(FlutterError(code: "INVALID_PARAM", message: "参数错误：access_key或app_key参数不能为空", details: nil))
            return
        }
        let tempKey = appkey + "0000000000000000"
        let key = String(tempKey.prefix(16)) // len: 16
        guard let accesskey = try? AES(keyString: key).decrypt(secrect) else {
            result(FlutterError(code: "INVALID_PARAM", message: "参数错误：secret参数不能为空", details: nil))
            return
        }
        
        guard let args = call.arguments as? [String: Any],
              let agreement = args["userAgreementUrl"] as? String,
              let policy = args["privacyPolicyUrl"] as? String else {
            result(FlutterError(code: "INVALID_PARAM", message: "参数错误：agreement和policy参数不能为空", details: nil))
            return
        }
        
        // 获取authAppList数据
        if let authAppList = args["authAppList"] as? [[String: Any]] {
            ATPhoneAuth.instance.authAppList = authAppList
            DouluoManagerChannel.debugLog("保存authAppList数据: \(authAppList.count)个游戏")
            print("保存authAppList数据: \(authAppList)")
        }
        
        let agreementText   = componentsAddressIn(url: agreement)
        let policyText = componentsAddressIn(url: policy)
        
        guard let agreementText = agreementText,
              let policyText = policyText else {
            result(FlutterError(code: "INVALID_PARAM", message: "参数错误：agreement和policy截取失败", details: nil))
            return
        }
        // 调用ATPhoneAuth的configFlashVerify方法
//        var tempAccesskey = "dAL5xyh2824sR20Vv3lNyVqIgSCPN4WvamWiqN8ZfEVI1vMbNq+eEd2Oyb9aQnYkqoHGLSnXETxv87t11g7brESQHa4zZJeXJnRgaqEbfKuIvw2TJ0I7M2lARW5by72AfdsnXbryqw2fiTLy59WXFAYoBo9/ovEhySUzVikTnpIBH0lCEUlkqGNTPbu9Nt9/sOMEjVpoSj2KzW6SGYVpw/eeZ0aR/5DE12m8HBgS7WOWsYgUxbzuEyWfGeDjocjS"
        // var tempAccesskey =    "dAL5xyh2824sR20Vv3lNyVqIgSCPN4WvamWiqN8ZfEVI1vMbNq+eEd2Oyb9aQnYkqoHGLSnXETxv87t11g7brESQHa4zZJeXJnRgaqEbfKuIvw2TJ0I7M2lARW5by72AfdsnXbryqw2fiTLy59WXFAYoBo9/ovEhySUzVikTnpIBH0lCEUlkqGNTPbu9Nt9/sOMEjVpoSj2KzW6SGYVpw/eeZ0aR/5DE12m8HBgS7WOWsYgUxbzuEyWfGeDjocjS"
        ATPhoneAuth.instance.configFlashVerify(accesskey, policy: policyText, agreement: agreementText) { (sdkInfo) in
            DouluoManagerChannel.debugLog("闪验SDK配置结果: \(sdkInfo)")
            if let resultCode = sdkInfo["resultCode"] as? String, resultCode == PNSCodeSuccess {
                DouluoManagerChannel.debugLog("闪验SDK配置成功")
                result(true)
            }
            else {
                DouluoManagerChannel.debugLog("闪验SDK配置失败")
                result(false)
            }
        }
    }
    
    func componentsAddressIn(url: String?) -> String? {
        let keyValues = url?.components(separatedBy: "?").last
        if let keyValues = keyValues {
            let array = keyValues.components(separatedBy: "&")
            let value = array.filter { (result) -> Bool in
                result.hasPrefix("url=")
            }.last
            let encodeUrl = value?.components(separatedBy: "=").last
            return encodeUrl?.removingPercentEncoding
        }
        return nil
    }
    
//    private func oneKeyLogin(call: FlutterMethodCall, result: @escaping FlutterResult) {
//        print("call.arguments: \(String(describing: call.arguments))")
//        
//        guard let args = call.arguments as? [String: Any] else {
//            result(FlutterError(code: "INVALID_PARAM", message: "参数错误：参数不能为空", details: nil))
//            return
//        }
//        
//        // 获取当前的根视图控制器
//        guard let rootViewController = UIApplication.shared.keyWindow?.rootViewController else {
//            result(FlutterError(code: "NO_ROOT_CONTROLLER", message: "无法获取根视图控制器", details: nil))
//            return
//        }
//        
//        let silent = args["silent"] as? Bool ?? false
//        let topBannerImage = args["topBannerImage"] as? UIImage
//        
//        // 调用ATPhoneAuth的oneKeyLogin方法
//        ATPhoneAuth.instance.oneKeyLogin(in: rootViewController, silent: silent, topBannerImage: topBannerImage) { (responseDict, error) in
//            if let error = error {
//                result(FlutterError(code: "ONE_KEY_LOGIN_ERROR", message: error.localizedDescription, details: nil))
//            } else {
//                result(responseDict)
//            }
//        }
//    }
    
    private func checkNetworkType(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(statusFrom(connectivityTypes: connectivityProvider.currentConnectivityTypes))
    }
    
    private func statusFrom(connectivityType: ConnectivityType) -> String {
      switch connectivityType {
      case .wifi:
        return "wifi"
      case .cellular:
        return "mobile"
      case .wiredEthernet:
        return "ethernet"
      case .other:
          return "other"
      case .none:
        return "none"
      }
    }
    
    private func statusFrom(connectivityTypes: [ConnectivityType]) -> [String] {
      return connectivityTypes.map {
        self.statusFrom(connectivityType: $0)
      }
    }
    
    /// 发送闪验UI事件到Flutter端
    func sendFastLoginUIEvent(event: String, arguments: [String: Any] = [:]) {
        DouluoManagerChannel.debugLog("发送闪验UI事件: \(event), 参数: \(arguments)")
        DispatchQueue.main.async {
            var eventArgs = ["event": event]
            // 合并额外的参数
            for (key, value) in arguments {
                eventArgs[key] = value as? String ?? ""
            }
            self.methodChannel?.invokeMethod("onFastLoginUIEvent", arguments: eventArgs)
        }
    }
    
    /// 发送绑定成功事件到Flutter端
    func sendBindingSuccessEvent() {
        DouluoManagerChannel.debugLog("发送绑定成功事件")
        DispatchQueue.main.async {
            self.methodChannel?.invokeMethod("onBindingSuccess", arguments: [])
        }
    }
    
    func sendCheckAuthAppStatusIfNeededEvent() {
        DispatchQueue.main.async {
            self.methodChannel?.invokeMethod("checkAuthAppStatusIfNeeded", arguments: []) { result in
                if result as? Bool ?? false {
                    HUD.info("登录成功")
                    ATPhoneAuth.instance.cancelOneKeyLogin()
                }
            }
        }
    }

    /// 执行闪验登录
    private func doFastLogin(call: FlutterMethodCall, result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("执行闪验登录")
        
        // 获取当前的根视图控制器
        guard let rootViewController = UIApplication.shared.keyWindow?.rootViewController else {
            DouluoManagerChannel.debugLog("无法获取根视图控制器")
            result(FlutterError(code: "NO_ROOT_CONTROLLER", message: "无法获取根视图控制器", details: nil))
            return
        }
        
        let silent = false
        let topBannerImage = UIImage(named: "login_top_banner")
        
        // 调用ATPhoneAuth的oneKeyLogin方法
        ATPhoneAuth.instance.oneKeyLogin(in: rootViewController, silent: silent, topBannerImage: topBannerImage) { (responseDict, error) in
            if let error = error {
                DouluoManagerChannel.debugLog("闪验登录失败: \(error.localizedDescription)")
                result(FlutterError(code: "ONE_KEY_LOGIN_ERROR", message: error.localizedDescription, details: nil))
            } else if let responseDict = responseDict {
                // 检查是否是闪验登录成功
                if let clickType = responseDict["clickType"] as? String,
                   clickType == "atPhoneAuth",
                   let success = responseDict["success"] as? Bool,
                   success == true,
                   let token = responseDict["token"] as? String {
                    // 闪验登录成功，返回token
                    DouluoManagerChannel.debugLog("闪验登录成功，获取到token")
                    result(["success": true, "token": token])
                } else {
                    // 其他情况（用户选择了其他登录方式或取消）通过事件监听处理
                    DouluoManagerChannel.debugLog("用户选择了其他登录方式或取消")
                    result(["success": false, "message": "用户选择了其他登录方式或取消"])
                }
            } else {
                DouluoManagerChannel.debugLog("闪验登录未知错误")
                result(FlutterError(code: "UNKNOWN_ERROR", message: "未知错误", details: nil))
            }
        }
    }
    
    /// 关闭闪验登录页
    private func cancelOneKeyLogin(call: FlutterMethodCall, result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("关闭闪验登录页")
        ATPhoneAuth.instance.cancelOneKeyLogin()
    }
    
    /// 检查闪验环境是否支持
    private func checkFastLoginEnvironment(call: FlutterMethodCall, result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("检查闪验环境是否支持")

        // 使用ATPhoneAuth检查闪验环境
        let currentSupportStatus = ATPhoneAuth.instance.isCanUseOneKeyLogin
        
        // 如果尚未初始化过，进行环境检测
        if currentSupportStatus == nil {
            DouluoManagerChannel.debugLog("闪验环境尚未检测，开始异步检测")
            // 异步检测环境
            TXCommonHandler.sharedInstance().checkEnvAvailable(
                with: PNSAuthType.loginToken,
                complete: { resultDic in
                    let isSupported = resultDic?["resultCode"] as? String == PNSCodeSuccess
                    DouluoManagerChannel.debugLog("闪验环境检测完成，支持状态: \(isSupported)")
                    DispatchQueue.main.async {
                        result(isSupported)
                    }
                }
            )
        } else {
            // 已经有缓存结果，直接返回
            DouluoManagerChannel.debugLog("使用缓存的闪验环境检测结果: \(currentSupportStatus!)")
            result(currentSupportStatus)
        }
    }
    
    /// 获取IDFA (广告标识符)
    private func getIDFA(result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("获取IDFA")
        // 检查是否支持广告追踪
        if ASIdentifierManager.shared().isAdvertisingTrackingEnabled {
            // 获取广告标识符
            let idfa = ASIdentifierManager.shared().advertisingIdentifier.uuidString
            DouluoManagerChannel.debugLog("获取到IDFA: \(idfa)")
            result(idfa)
        } else {
            // 用户未授权广告追踪或设备不支持
            DouluoManagerChannel.debugLog("用户未授权广告追踪或设备不支持IDFA")
            result("")
        }
    }
    
    /// 请求广告追踪权限
    private func requestTrackingPermission(result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("请求广告追踪权限")
        // 检查系统版本是否支持 App Tracking Transparency
        if #available(iOS 14.5, *) {
            // 检查当前授权状态
            let status = ATTrackingManager.trackingAuthorizationStatus
            DouluoManagerChannel.debugLog("当前广告追踪权限状态: \(status.rawValue)")
            
            switch status {
            case .notDetermined:
                DouluoManagerChannel.debugLog("用户尚未做出选择，请求权限")
                // 用户尚未做出选择，请求权限
                ATTrackingManager.requestTrackingAuthorization { status in
                    DispatchQueue.main.async {
                        let statusString: String
                        switch status {
                        case .authorized:
                            statusString = "authorized"
                        case .denied:
                            statusString = "denied"
                        case .restricted:
                            statusString = "restricted"
                        case .notDetermined:
                            statusString = "notDetermined"
                        @unknown default:
                            statusString = "unknown"
                        }
                        DouluoManagerChannel.debugLog("用户权限选择结果: \(statusString)")
                        result(statusString)
                    }
                }
            case .authorized:
                DouluoManagerChannel.debugLog("已授权广告追踪")
                result("authorized")
            case .denied:
                DouluoManagerChannel.debugLog("已拒绝广告追踪")
                result("denied")
            case .restricted:
                DouluoManagerChannel.debugLog("广告追踪受限")
                result("restricted")
            @unknown default:
                DouluoManagerChannel.debugLog("未知的广告追踪状态")
                result("unknown")
            }
        } else {
            // iOS 14.5 以下版本，直接返回已授权
            DouluoManagerChannel.debugLog("iOS 14.5 以下版本，直接返回已授权")
            result("authorized")
        }
    }
    
    /// 检查通知权限是否已授权
    private func areNotificationsEnabled(result: @escaping FlutterResult) {
        DouluoManagerChannel.debugLog("检查通知权限状态")
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                let isEnabled = settings.authorizationStatus == .authorized
                DouluoManagerChannel.debugLog("通知权限状态: \(isEnabled ? "已授权" : "未授权")")
                result(isEnabled)
            }
        }
    }
}

